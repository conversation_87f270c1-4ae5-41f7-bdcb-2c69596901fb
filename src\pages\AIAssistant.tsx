import { AIChat } from "@/components/AIChat";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>rkles, Database, Shield } from "lucide-react";

export default function AIAssistant() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2">
            <Bot className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold">AI Food Assistant</h1>
            <Sparkles className="h-6 w-6 text-yellow-500" />
          </div>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Get personalized food recommendations based on your preferences, dietary needs, and goals. 
            Our AI assistant uses real restaurant data to provide accurate suggestions.
          </p>
          
          <div className="flex items-center justify-center gap-2 flex-wrap">
            <Badge variant="secondary" className="flex items-center gap-1">
              <Database className="h-3 w-3" />
              Real Data
            </Badge>
            <Badge variant="secondary" className="flex items-center gap-1">
              <Shield className="h-3 w-3" />
              No Hallucination
            </Badge>
            <Badge variant="secondary">
              Multilingual Support
            </Badge>
            <Badge variant="secondary">
              Personalized
            </Badge>
          </div>
        </div>

        {/* Main Chat Interface */}
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Chat Component */}
          <div className="flex-1">
            <AIChat />
          </div>

          {/* Info Panel */}
          <div className="lg:w-80 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  How it works
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
                    1
                  </div>
                  <div>
                    <p className="font-medium">Real Data Analysis</p>
                    <p className="text-muted-foreground">
                      We analyze real menu items from restaurants in our database
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
                    2
                  </div>
                  <div>
                    <p className="font-medium">Personal Matching</p>
                    <p className="text-muted-foreground">
                      Match items with your dietary preferences and restrictions
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
                    3
                  </div>
                  <div>
                    <p className="font-medium">Smart Recommendations</p>
                    <p className="text-muted-foreground">
                      Get accurate suggestions with real prices and details
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Sample Questions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="p-2 bg-muted rounded text-muted-foreground">
                  "What healthy options do you have under $15?"
                </div>
                <div className="p-2 bg-muted rounded text-muted-foreground">
                  "I need a high-protein meal for muscle gain"
                </div>
                <div className="p-2 bg-muted rounded text-muted-foreground">
                  "Show me vegan options with low calories"
                </div>
                <div className="p-2 bg-muted rounded text-muted-foreground">
                  "What can I eat if I'm allergic to nuts?"
                </div>
                <div className="p-2 bg-muted rounded text-muted-foreground">
                  "Recommend something spicy for dinner"
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Real restaurant data</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Dietary restrictions support</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Allergy filtering</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Calorie goal alignment</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Price-based filtering</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Order history analysis</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Multi-language support</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Supported Languages</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center gap-2">
                    <span>🇺🇸</span>
                    <span>English</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🇹🇷</span>
                    <span>Türkçe</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🇦🇿</span>
                    <span>Azərbaycan</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🇷🇺</span>
                    <span>Русский</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🇸🇦</span>
                    <span>العربية</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>
            Powered by advanced AI and real restaurant data. 
            All recommendations are based on actual menu items and prices.
          </p>
        </div>
      </div>
    </div>
  );
}
