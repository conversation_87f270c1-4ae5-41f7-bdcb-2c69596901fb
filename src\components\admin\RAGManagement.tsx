import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  RefreshCw, 
  Database, 
  Users, 
  ChefHat, 
  Activity,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";
import { toast } from "sonner";
import { DataSyncService } from "@/services/DataSyncService";
import { VectorEmbeddingService } from "@/services/VectorEmbeddingService";
import { RAGService } from "@/services/RAGService";

interface SyncStatus {
  menuItemsCount: number;
  userProfilesCount: number;
  lastSyncTime: Date;
}

export const RAGManagement = () => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);
  const [testQuery, setTestQuery] = useState("");
  const [testResult, setTestResult] = useState<any>(null);
  const [isTestingRAG, setIsTestingRAG] = useState(false);

  const dataSyncService = new DataSyncService();
  const vectorService = new VectorEmbeddingService();
  const ragService = new RAGService();

  useEffect(() => {
    loadSyncStatus();
    // Initialize data sync
    dataSyncService.initializeSync();

    return () => {
      dataSyncService.stopSync();
    };
  }, []);

  const loadSyncStatus = async () => {
    try {
      setIsLoading(true);
      const status = await dataSyncService.getSyncStatus();
      setSyncStatus(status);
    } catch (error) {
      console.error("Error loading sync status:", error);
      toast.error("Failed to load sync status");
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualSync = async (type: "menu" | "profiles" | "all") => {
    try {
      setIsSyncing(true);
      setSyncProgress(0);
      
      if (type === "menu" || type === "all") {
        toast.info("Starting menu items synchronization...");
        await dataSyncService.syncAllMenuItems();
        setSyncProgress(type === "all" ? 50 : 100);
      }
      
      if (type === "profiles" || type === "all") {
        toast.info("Starting user profiles synchronization...");
        await dataSyncService.syncAllUserProfiles();
        setSyncProgress(100);
      }
      
      toast.success(`${type === "all" ? "All data" : type} synchronized successfully!`);
      await loadSyncStatus();
    } catch (error) {
      console.error("Error during manual sync:", error);
      toast.error(`Failed to sync ${type}`);
    } finally {
      setIsSyncing(false);
      setSyncProgress(0);
    }
  };

  const handleForceRefresh = async () => {
    try {
      setIsSyncing(true);
      toast.info("Force refreshing all embeddings...");
      await dataSyncService.forceRefreshAllEmbeddings();
      toast.success("All embeddings refreshed successfully!");
      await loadSyncStatus();
    } catch (error) {
      console.error("Error during force refresh:", error);
      toast.error("Failed to refresh embeddings");
    } finally {
      setIsSyncing(false);
    }
  };

  const handleTestRAG = async () => {
    if (!testQuery.trim()) {
      toast.error("Please enter a test query");
      return;
    }

    try {
      setIsTestingRAG(true);
      setTestResult(null);
      
      // For testing, we'll use a mock user ID
      const mockUserId = "test-user-123";
      
      const result = await ragService.generatePersonalizedRecommendation(
        testQuery,
        mockUserId,
        "en"
      );
      
      setTestResult(result);
      toast.success("RAG test completed successfully!");
    } catch (error) {
      console.error("Error testing RAG:", error);
      toast.error("RAG test failed");
      setTestResult({ error: error.message });
    } finally {
      setIsTestingRAG(false);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">RAG System Management</h2>
        <Button
          onClick={loadSyncStatus}
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
          Refresh Status
        </Button>
      </div>

      <Tabs defaultValue="status" className="space-y-4">
        <TabsList>
          <TabsTrigger value="status">System Status</TabsTrigger>
          <TabsTrigger value="sync">Data Synchronization</TabsTrigger>
          <TabsTrigger value="test">RAG Testing</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Menu Items</CardTitle>
                <ChefHat className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {syncStatus?.menuItemsCount || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Available menu items
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">User Profiles</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {syncStatus?.userProfilesCount || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Registered users
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Last Sync</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-sm font-bold">
                  {syncStatus ? formatDate(syncStatus.lastSyncTime) : "Never"}
                </div>
                <p className="text-xs text-muted-foreground">
                  System last updated
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Vector Embeddings</span>
                  <Badge variant="default" className="bg-green-500">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Data Synchronization</span>
                  <Badge variant="default" className="bg-green-500">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Running
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">RAG Pipeline</span>
                  <Badge variant="default" className="bg-green-500">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Operational
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sync" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Data Synchronization
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {isSyncing && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Synchronization Progress</span>
                    <span>{syncProgress}%</span>
                  </div>
                  <Progress value={syncProgress} className="w-full" />
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Menu Items</h4>
                  <p className="text-sm text-muted-foreground">
                    Sync all menu items and create vector embeddings
                  </p>
                  <Button
                    onClick={() => handleManualSync("menu")}
                    disabled={isSyncing}
                    className="w-full"
                  >
                    {isSyncing ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <ChefHat className="h-4 w-4 mr-2" />
                    )}
                    Sync Menu Items
                  </Button>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">User Profiles</h4>
                  <p className="text-sm text-muted-foreground">
                    Sync all user profiles and create preference embeddings
                  </p>
                  <Button
                    onClick={() => handleManualSync("profiles")}
                    disabled={isSyncing}
                    className="w-full"
                  >
                    {isSyncing ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Users className="h-4 w-4 mr-2" />
                    )}
                    Sync User Profiles
                  </Button>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={() => handleManualSync("all")}
                  disabled={isSyncing}
                  className="flex-1"
                  variant="default"
                >
                  {isSyncing ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Database className="h-4 w-4 mr-2" />
                  )}
                  Sync All Data
                </Button>

                <Button
                  onClick={handleForceRefresh}
                  disabled={isSyncing}
                  variant="outline"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Force Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>RAG System Testing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Test Query</label>
                <input
                  type="text"
                  value={testQuery}
                  onChange={(e) => setTestQuery(e.target.value)}
                  placeholder="Enter a food recommendation query..."
                  className="w-full px-3 py-2 border rounded-md"
                />
              </div>

              <Button
                onClick={handleTestRAG}
                disabled={isTestingRAG || !testQuery.trim()}
                className="w-full"
              >
                {isTestingRAG ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Activity className="h-4 w-4 mr-2" />
                )}
                Test RAG System
              </Button>

              {testResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Test Results</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {testResult.error ? (
                      <div className="flex items-center gap-2 text-red-600">
                        <AlertCircle className="h-4 w-4" />
                        <span>Error: {testResult.error}</span>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">AI Response:</h4>
                          <p className="text-sm bg-muted p-3 rounded">
                            {testResult.response}
                          </p>
                        </div>

                        {testResult.recommendations && testResult.recommendations.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-2">Recommendations:</h4>
                            <div className="space-y-2">
                              {testResult.recommendations.map((rec: any, index: number) => (
                                <div key={index} className="bg-muted p-2 rounded text-sm">
                                  <div className="font-medium">{rec.name}</div>
                                  <div className="text-muted-foreground">
                                    {rec.restaurantName} • ${rec.price}
                                  </div>
                                  <div className="text-xs mt-1">{rec.reasoning}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="text-xs text-muted-foreground">
                          Confidence: {Math.round((testResult.confidence || 0) * 100)}%
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
