import { firestore, model } from "@/config/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  collectionGroup,
  orderBy,
  limit,
} from "firebase/firestore";
import { VectorEmbeddingService } from "./VectorEmbeddingService";
import { UserContextService } from "./UserContextService";

export interface RAGResponse {
  response: string;
  recommendations: FoodRecommendation[];
  confidence: number;
  sources: string[];
}

export interface FoodRecommendation {
  itemId: string;
  name: string;
  restaurantName: string;
  restaurantId: string;
  price: number;
  description: string;
  calories?: number;
  matchScore: number;
  reasoning: string;
  category: string;
  dietary: string[];
  allergens?: string[];
}

export interface UserContext {
  preferences: {
    favoriteCategories: string[];
    dietaryRestrictions: string[];
    allergies: string[];
    dislikedIngredients: string[];
    preferredCuisines: string[];
    spiceLevel: string;
  };
  goals: {
    calorieGoal: number;
    dietaryGoal: string; // "lose", "gain", "maintain"
    healthGoals: string[];
  };
  orderHistory: {
    frequentItems: string[];
    frequentRestaurants: string[];
    averageOrderValue: number;
    lastOrderDate: Date;
  };
  location?: {
    latitude: number;
    longitude: number;
  };
}

export class RAGService {
  private vectorService: VectorEmbeddingService;
  private contextService: UserContextService;

  constructor() {
    this.vectorService = new VectorEmbeddingService();
    this.contextService = new UserContextService();
  }

  /**
   * Generate personalized food recommendation using RAG
   */
  async generatePersonalizedRecommendation(
    userQuery: string,
    userId: string,
    language: string = "en",
    userContext?: UserContext
  ): Promise<RAGResponse> {
    try {
      // 1. Get user context if not provided
      const context =
        userContext || (await this.contextService.getUserContext(userId));

      // 2. Generate query embedding
      const queryEmbedding = await this.vectorService.generateEmbedding(
        userQuery
      );

      // 3. Perform hybrid search to find relevant menu items
      const relevantItems = await this.performHybridSearch(
        queryEmbedding,
        context,
        10 // Get top 10 matches
      );

      // 4. Get user's recent orders for additional context
      const recentOrders = await this.getUserRecentOrders(userId, 5);

      // 5. Construct augmented prompt
      const augmentedPrompt = this.constructPrompt(
        userQuery,
        context,
        relevantItems,
        recentOrders,
        language
      );

      // 6. Generate response using Gemini
      const result = await model.generateContent(augmentedPrompt);
      const response = result.response.text();

      // 7. Extract recommendations from the response
      const recommendations = this.extractRecommendations(
        relevantItems,
        response
      );

      return {
        response,
        recommendations,
        confidence: this.calculateConfidence(relevantItems, context),
        sources: relevantItems.map(
          (item) => `${item.restaurantName}: ${item.name}`
        ),
      };
    } catch (error) {
      console.error("Error in RAG service:", error);
      throw new Error("Failed to generate personalized recommendation");
    }
  }

  /**
   * Perform hybrid search combining semantic matching with metadata filtering
   * Uses real Firestore data and semantic text matching
   */
  private async performHybridSearch(
    queryEmbedding: number[],
    context: UserContext,
    limitCount: number = 10
  ): Promise<any[]> {
    try {
      console.log("Starting hybrid search with user context:", context);

      // Get all available menu items from all restaurants
      const menuItemsRef = collectionGroup(firestore, "menu");
      const menuQuery = query(menuItemsRef, where("available", "==", true));

      const snapshot = await getDocs(menuQuery);
      console.log(`Found ${snapshot.docs.length} available menu items`);

      if (snapshot.empty) {
        console.log("No menu items found in database");
        return [];
      }

      const allItems = snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          itemId: doc.id,
          ...data,
          // Get restaurant info from the document path
          restaurantId: doc.ref.parent.parent?.id,
        };
      });

      // Get restaurant names for the items
      const itemsWithRestaurantInfo = await this.enrichWithRestaurantInfo(
        allItems
      );

      // Filter items based on user preferences and restrictions
      const filteredItems = this.filterItemsByUserPreferences(
        itemsWithRestaurantInfo,
        context
      );

      console.log(
        `Filtered to ${filteredItems.length} items based on user preferences`
      );

      // Calculate semantic and preference-based scores
      const scoredItems = filteredItems.map((item) => ({
        ...item,
        matchScore: this.calculateComprehensiveMatchScore(
          item,
          context,
          queryEmbedding
        ),
      }));

      // Sort by match score and return top results
      const sortedItems = scoredItems
        .sort((a, b) => b.matchScore - a.matchScore)
        .slice(0, limitCount);

      console.log(`Returning top ${sortedItems.length} items`);
      return sortedItems;
    } catch (error) {
      console.error("Error in hybrid search:", error);
      return [];
    }
  }

  /**
   * Enrich menu items with restaurant information
   */
  private async enrichWithRestaurantInfo(items: any[]): Promise<any[]> {
    const restaurantIds = [
      ...new Set(items.map((item) => item.restaurantId).filter(Boolean)),
    ];
    const restaurantInfoMap = new Map();

    // Fetch restaurant information in batches
    for (const restaurantId of restaurantIds) {
      try {
        const restaurantRef = doc(firestore, "restaurants", restaurantId);
        const restaurantSnap = await getDoc(restaurantRef);

        if (restaurantSnap.exists()) {
          const restaurantData = restaurantSnap.data();
          restaurantInfoMap.set(restaurantId, {
            restaurantName: restaurantData.restaurantName,
            cuisines: restaurantData.cuisines || [],
            categories: restaurantData.categories || [],
            priceRange: restaurantData.priceRange,
          });
        }
      } catch (error) {
        console.error(`Error fetching restaurant ${restaurantId}:`, error);
      }
    }

    // Enrich items with restaurant info
    return items.map((item) => {
      const restaurantInfo = restaurantInfoMap.get(item.restaurantId);
      return {
        ...item,
        restaurantName: restaurantInfo?.restaurantName || "Unknown Restaurant",
        restaurantCuisines: restaurantInfo?.cuisines || [],
        restaurantCategories: restaurantInfo?.categories || [],
        restaurantPriceRange: restaurantInfo?.priceRange,
      };
    });
  }

  /**
   * Calculate match score for an item based on user context
   */
  private calculateMatchScore(
    item: any,
    context: UserContext,
    queryEmbedding: number[]
  ): number {
    let score = 0;

    // Category preference match
    if (context.preferences.favoriteCategories.includes(item.category)) {
      score += 0.3;
    }

    // Dietary preference match
    if (
      item.dietary &&
      context.preferences.dietaryRestrictions.some((diet: string) =>
        item.dietary.includes(diet)
      )
    ) {
      score += 0.2;
    }

    // Allergen check (negative score if contains allergens)
    if (
      item.allergens &&
      context.preferences.allergies.some((allergen: string) =>
        item.allergens.includes(allergen)
      )
    ) {
      score -= 0.5;
    }

    // Calorie goal alignment
    if (item.calories && context.goals.calorieGoal > 0) {
      const calorieRatio = item.calories / context.goals.calorieGoal;
      if (calorieRatio <= 0.3) {
        // Good portion of daily calories
        score += 0.2;
      } else if (calorieRatio > 0.5) {
        // Too high calorie
        score -= 0.1;
      }
    }

    // Price consideration (prefer items within reasonable range)
    if (item.price <= context.orderHistory.averageOrderValue * 1.2) {
      score += 0.1;
    }

    return Math.max(0, Math.min(1, score)); // Normalize between 0 and 1
  }

  /**
   * Filter menu items based on user preferences and restrictions
   */
  private filterItemsByUserPreferences(
    items: any[],
    context: UserContext
  ): any[] {
    return items.filter((item) => {
      // Filter out items with allergens
      if (item.allergens && context.preferences.allergies.length > 0) {
        const hasAllergen = item.allergens.some((allergen: string) =>
          context.preferences.allergies.includes(allergen)
        );
        if (hasAllergen) {
          console.log(
            `Filtering out ${item.name} due to allergen: ${item.allergens}`
          );
          return false;
        }
      }

      // Filter out items with disliked ingredients
      if (
        item.ingredients &&
        context.preferences.dislikedIngredients.length > 0
      ) {
        const hasDislikedIngredient = item.ingredients.some(
          (ingredient: string) =>
            context.preferences.dislikedIngredients.some((disliked: string) =>
              ingredient.toLowerCase().includes(disliked.toLowerCase())
            )
        );
        if (hasDislikedIngredient) {
          console.log(`Filtering out ${item.name} due to disliked ingredient`);
          return false;
        }
      }

      // Filter by calorie goals (if specified)
      if (context.goals.calorieGoal > 0 && item.calories) {
        // Allow items up to 50% of daily calorie goal for a single meal
        const maxCaloriesPerMeal = context.goals.calorieGoal * 0.5;
        if (item.calories > maxCaloriesPerMeal) {
          console.log(
            `Filtering out ${item.name} due to high calories: ${item.calories}`
          );
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Calculate comprehensive match score combining semantic and preference matching
   */
  private calculateComprehensiveMatchScore(
    item: any,
    context: UserContext,
    queryEmbedding: number[]
  ): number {
    let score = 0;
    let maxScore = 0;

    // 1. Category preference match (20% weight)
    maxScore += 0.2;
    if (context.preferences.favoriteCategories.length > 0) {
      if (context.preferences.favoriteCategories.includes(item.category)) {
        score += 0.2;
      }
    } else {
      // If no preferences set, give neutral score
      score += 0.1;
    }

    // 2. Dietary preference match (20% weight)
    maxScore += 0.2;
    if (item.dietary && item.dietary.length > 0) {
      if (context.preferences.dietaryRestrictions.length > 0) {
        const dietaryMatch = item.dietary.some((diet: string) =>
          context.preferences.dietaryRestrictions.includes(diet)
        );
        if (dietaryMatch) {
          score += 0.2;
        }
      } else {
        // If no dietary restrictions, give neutral score
        score += 0.1;
      }
    }

    // 3. Cuisine preference match (15% weight)
    maxScore += 0.15;
    if (
      item.restaurantCuisines &&
      context.preferences.preferredCuisines.length > 0
    ) {
      const cuisineMatch = item.restaurantCuisines.some((cuisine: string) =>
        context.preferences.preferredCuisines.includes(cuisine)
      );
      if (cuisineMatch) {
        score += 0.15;
      }
    } else {
      score += 0.075; // Neutral score
    }

    // 4. Price alignment (15% weight)
    maxScore += 0.15;
    if (item.price && context.orderHistory.averageOrderValue > 0) {
      const priceRatio = item.price / context.orderHistory.averageOrderValue;
      if (priceRatio <= 1.2) {
        // Within 20% of average
        score += 0.15;
      } else if (priceRatio <= 1.5) {
        // Within 50% of average
        score += 0.1;
      } else {
        score += 0.05; // Expensive but still some score
      }
    } else {
      score += 0.075; // Neutral score
    }

    // 5. Calorie goal alignment (15% weight)
    maxScore += 0.15;
    if (item.calories && context.goals.calorieGoal > 0) {
      const calorieRatio = item.calories / (context.goals.calorieGoal * 0.33); // Assume 1/3 of daily calories per meal
      if (calorieRatio <= 1.0) {
        // Perfect portion
        score += 0.15;
      } else if (calorieRatio <= 1.3) {
        // Slightly high but acceptable
        score += 0.1;
      } else {
        score += 0.05; // High calorie but still some score
      }
    } else {
      score += 0.075; // Neutral score
    }

    // 6. Order history match (15% weight)
    maxScore += 0.15;
    if (context.orderHistory.frequentItems.length > 0) {
      const isFrequentItem = context.orderHistory.frequentItems.some(
        (frequentItem) =>
          item.name.toLowerCase().includes(frequentItem.toLowerCase()) ||
          frequentItem.toLowerCase().includes(item.name.toLowerCase())
      );
      if (isFrequentItem) {
        score += 0.15;
      } else {
        score += 0.05; // New item, lower score but not zero
      }
    } else {
      score += 0.075; // Neutral score
    }

    // Normalize score to 0-1 range
    const normalizedScore = maxScore > 0 ? score / maxScore : 0;

    console.log(`Item: ${item.name}, Score: ${normalizedScore.toFixed(3)}`);
    return normalizedScore;
  }

  /**
   * Get user's recent orders for context
   */
  private async getUserRecentOrders(
    userId: string,
    limitCount: number = 5
  ): Promise<any[]> {
    try {
      const ordersRef = collection(firestore, "orders");
      const ordersQuery = query(
        ordersRef,
        where("userId", "==", userId),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      );

      const snapshot = await getDocs(ordersQuery);
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error("Error fetching recent orders:", error);
      return [];
    }
  }

  /**
   * Construct the augmented prompt for the LLM
   */
  private constructPrompt(
    userQuery: string,
    context: UserContext,
    relevantItems: any[],
    recentOrders: any[],
    language: string
  ): string {
    const languageInstructions = {
      en: "Respond in English",
      tr: "Türkçe olarak yanıtla",
      az: "Azərbaycan dilində cavab ver",
      ru: "Отвечай на русском языке",
      ar: "أجب باللغة العربية",
    };

    const instruction =
      languageInstructions[language as keyof typeof languageInstructions] ||
      languageInstructions.en;

    // Build detailed menu options from real data
    const menuOptionsText = relevantItems
      .slice(0, 5)
      .map((item, index) => {
        const parts = [
          `${index + 1}. **${item.name}** - ${item.restaurantName}`,
          `   Price: $${item.price}`,
        ];

        if (item.description) {
          parts.push(`   Description: ${item.description}`);
        }

        if (item.calories) {
          parts.push(`   Calories: ${item.calories}`);
        }

        if (item.category) {
          parts.push(`   Category: ${item.category}`);
        }

        if (item.dietary && item.dietary.length > 0) {
          parts.push(`   Dietary Options: ${item.dietary.join(", ")}`);
        }

        if (item.ingredients && item.ingredients.length > 0) {
          parts.push(
            `   Key Ingredients: ${item.ingredients.slice(0, 5).join(", ")}`
          );
        }

        if (item.spicyLevel && item.spicyLevel !== "none") {
          parts.push(`   Spice Level: ${item.spicyLevel}`);
        }

        parts.push(`   Match Score: ${(item.matchScore * 100).toFixed(1)}%`);

        return parts.join("\n");
      })
      .join("\n\n");

    // Build recent orders text
    const recentOrdersText =
      recentOrders
        .slice(0, 3)
        .map((order) => {
          const itemNames =
            order.items?.map((item: any) => item.name).join(", ") ||
            "Unknown items";
          const restaurantName = order.restaurantName || "Unknown restaurant";
          const orderDate = order.createdAt?.toDate
            ? order.createdAt.toDate().toLocaleDateString()
            : "Unknown date";
          return `- ${itemNames} from ${restaurantName} (${orderDate})`;
        })
        .join("\n") || "No recent orders found";

    return `You are a professional food recommendation assistant for a restaurant platform. ${instruction}.

CRITICAL INSTRUCTIONS:
- You must ONLY recommend items from the "AVAILABLE MENU OPTIONS" list below
- Do NOT invent, create, or suggest any menu items that are not in the provided list
- Use EXACT names, prices, and restaurant names from the data provided
- If no suitable options exist, explain why and suggest the user modify their preferences
- Be specific about why each recommendation matches the user's profile

USER PROFILE:
- Dietary Restrictions: ${
      context.preferences.dietaryRestrictions.join(", ") || "None"
    }
- Allergies: ${context.preferences.allergies.join(", ") || "None"}
- Favorite Food Categories: ${
      context.preferences.favoriteCategories.join(", ") || "None specified"
    }
- Preferred Cuisines: ${
      context.preferences.preferredCuisines.join(", ") || "None specified"
    }
- Daily Calorie Goal: ${context.goals.calorieGoal || "Not specified"} calories
- Health Goal: ${context.goals.dietaryGoal || "Not specified"}
- Average Order Value: $${
      context.orderHistory.averageOrderValue || "Not available"
    }
- Disliked Ingredients: ${
      context.preferences.dislikedIngredients.join(", ") || "None"
    }

RECENT ORDER HISTORY:
${recentOrdersText}

AVAILABLE MENU OPTIONS (ONLY recommend from these):
${menuOptionsText || "No menu items available matching your criteria"}

USER QUESTION: "${userQuery}"

Based ONLY on the available menu options above, provide a personalized recommendation. Include:
1. Specific item name and restaurant (exactly as shown above)
2. Price and calories (exactly as shown above)
3. Why this item matches the user's preferences and goals
4. Any relevant dietary or nutritional information`;
  }

  /**
   * Extract structured recommendations from the LLM response
   */
  private extractRecommendations(
    relevantItems: any[],
    response: string
  ): FoodRecommendation[] {
    // For now, return the top relevant items as recommendations
    // This could be enhanced with NLP to extract specific recommendations from the response
    return relevantItems.slice(0, 3).map((item) => ({
      itemId: item.id || item.itemId,
      name: item.name,
      restaurantName: item.restaurantName || "Unknown Restaurant",
      restaurantId: item.restaurantId,
      price: item.price,
      description: item.description || "",
      calories: item.calories,
      matchScore: item.matchScore || 0,
      reasoning: `Recommended based on your preferences and dietary goals`,
      category: item.category || "",
      dietary: item.dietary || [],
      allergens: item.allergens || [],
    }));
  }

  /**
   * Calculate confidence score for the recommendation
   */
  private calculateConfidence(
    relevantItems: any[],
    context: UserContext
  ): number {
    if (relevantItems.length === 0) return 0;

    const avgMatchScore =
      relevantItems.reduce((sum, item) => sum + (item.matchScore || 0), 0) /
      relevantItems.length;
    const contextCompleteness = this.calculateContextCompleteness(context);

    return Math.min(1, avgMatchScore * 0.7 + contextCompleteness * 0.3);
  }

  /**
   * Calculate how complete the user context is
   */
  private calculateContextCompleteness(context: UserContext): number {
    let score = 0;
    let maxScore = 0;

    // Check various context fields
    if (context.preferences.dietaryRestrictions.length > 0) score += 0.2;
    maxScore += 0.2;

    if (context.preferences.favoriteCategories.length > 0) score += 0.2;
    maxScore += 0.2;

    if (context.goals.calorieGoal > 0) score += 0.2;
    maxScore += 0.2;

    if (context.orderHistory.frequentItems.length > 0) score += 0.2;
    maxScore += 0.2;

    if (context.preferences.allergies.length >= 0) score += 0.2; // Even empty array shows user provided info
    maxScore += 0.2;

    return maxScore > 0 ? score / maxScore : 0;
  }
}
