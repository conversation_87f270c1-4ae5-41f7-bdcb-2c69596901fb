import { firestore, model } from "@/config/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  collectionGroup,
  orderBy,
  limit,
} from "firebase/firestore";
import { VectorEmbeddingService } from "./VectorEmbeddingService";
import { UserContextService } from "./UserContextService";

export interface RAGResponse {
  response: string;
  recommendations: FoodRecommendation[];
  confidence: number;
  sources: string[];
}

export interface FoodRecommendation {
  itemId: string;
  name: string;
  restaurantName: string;
  restaurantId: string;
  price: number;
  description: string;
  calories?: number;
  matchScore: number;
  reasoning: string;
  category: string;
  dietary: string[];
  allergens?: string[];
}

export interface UserContext {
  preferences: {
    favoriteCategories: string[];
    dietaryRestrictions: string[];
    allergies: string[];
    dislikedIngredients: string[];
    preferredCuisines: string[];
    spiceLevel: string;
  };
  goals: {
    calorieGoal: number;
    dietaryGoal: string; // "lose", "gain", "maintain"
    healthGoals: string[];
  };
  orderHistory: {
    frequentItems: string[];
    frequentRestaurants: string[];
    averageOrderValue: number;
    lastOrderDate: Date;
  };
  location?: {
    latitude: number;
    longitude: number;
  };
}

export class RAGService {
  private vectorService: VectorEmbeddingService;
  private contextService: UserContextService;

  constructor() {
    this.vectorService = new VectorEmbeddingService();
    this.contextService = new UserContextService();
  }

  /**
   * Generate personalized food recommendation using RAG
   */
  async generatePersonalizedRecommendation(
    userQuery: string,
    userId: string,
    language: string = "en",
    userContext?: UserContext
  ): Promise<RAGResponse> {
    try {
      // 1. Get user context if not provided
      const context = userContext || await this.contextService.getUserContext(userId);

      // 2. Generate query embedding
      const queryEmbedding = await this.vectorService.generateEmbedding(userQuery);

      // 3. Perform hybrid search to find relevant menu items
      const relevantItems = await this.performHybridSearch(
        queryEmbedding,
        context,
        10 // Get top 10 matches
      );

      // 4. Get user's recent orders for additional context
      const recentOrders = await this.getUserRecentOrders(userId, 5);

      // 5. Construct augmented prompt
      const augmentedPrompt = this.constructPrompt(
        userQuery,
        context,
        relevantItems,
        recentOrders,
        language
      );

      // 6. Generate response using Gemini
      const result = await model.generateContent(augmentedPrompt);
      const response = result.response.text();

      // 7. Extract recommendations from the response
      const recommendations = this.extractRecommendations(relevantItems, response);

      return {
        response,
        recommendations,
        confidence: this.calculateConfidence(relevantItems, context),
        sources: relevantItems.map(item => `${item.restaurantName}: ${item.name}`)
      };

    } catch (error) {
      console.error("Error in RAG service:", error);
      throw new Error("Failed to generate personalized recommendation");
    }
  }

  /**
   * Perform hybrid search combining vector similarity with metadata filtering
   */
  private async performHybridSearch(
    queryEmbedding: number[],
    context: UserContext,
    limitCount: number = 10
  ): Promise<any[]> {
    try {
      // For now, we'll implement a simplified version without vector search
      // since Firestore vector search requires server-side setup
      // This will be enhanced once vector embeddings are properly set up

      const menuItemsRef = collectionGroup(firestore, "menu");
      let menuQuery = query(menuItemsRef, where("available", "==", true));

      // Apply dietary restrictions
      if (context.preferences.dietaryRestrictions.length > 0) {
        // Filter items that match dietary preferences
        menuQuery = query(
          menuQuery,
          where("dietary", "array-contains-any", context.preferences.dietaryRestrictions)
        );
      }

      // Apply calorie filtering if user has calorie goals
      if (context.goals.calorieGoal > 0) {
        menuQuery = query(
          menuQuery,
          where("calories", "<=", context.goals.calorieGoal)
        );
      }

      menuQuery = query(menuQuery, orderBy("price"), limit(limitCount));

      const snapshot = await getDocs(menuQuery);
      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        matchScore: this.calculateMatchScore(doc.data(), context, queryEmbedding)
      }));

      // Sort by match score
      return items.sort((a, b) => b.matchScore - a.matchScore);

    } catch (error) {
      console.error("Error in hybrid search:", error);
      return [];
    }
  }

  /**
   * Calculate match score for an item based on user context
   */
  private calculateMatchScore(item: any, context: UserContext, queryEmbedding: number[]): number {
    let score = 0;

    // Category preference match
    if (context.preferences.favoriteCategories.includes(item.category)) {
      score += 0.3;
    }

    // Dietary preference match
    if (item.dietary && context.preferences.dietaryRestrictions.some(
      (diet: string) => item.dietary.includes(diet)
    )) {
      score += 0.2;
    }

    // Allergen check (negative score if contains allergens)
    if (item.allergens && context.preferences.allergies.some(
      (allergen: string) => item.allergens.includes(allergen)
    )) {
      score -= 0.5;
    }

    // Calorie goal alignment
    if (item.calories && context.goals.calorieGoal > 0) {
      const calorieRatio = item.calories / context.goals.calorieGoal;
      if (calorieRatio <= 0.3) { // Good portion of daily calories
        score += 0.2;
      } else if (calorieRatio > 0.5) { // Too high calorie
        score -= 0.1;
      }
    }

    // Price consideration (prefer items within reasonable range)
    if (item.price <= context.orderHistory.averageOrderValue * 1.2) {
      score += 0.1;
    }

    return Math.max(0, Math.min(1, score)); // Normalize between 0 and 1
  }

  /**
   * Get user's recent orders for context
   */
  private async getUserRecentOrders(userId: string, limitCount: number = 5): Promise<any[]> {
    try {
      const ordersRef = collection(firestore, "orders");
      const ordersQuery = query(
        ordersRef,
        where("userId", "==", userId),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      );

      const snapshot = await getDocs(ordersQuery);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error("Error fetching recent orders:", error);
      return [];
    }
  }

  /**
   * Construct the augmented prompt for the LLM
   */
  private constructPrompt(
    userQuery: string,
    context: UserContext,
    relevantItems: any[],
    recentOrders: any[],
    language: string
  ): string {
    const languageInstructions = {
      en: "Respond in English",
      tr: "Türkçe olarak yanıtla",
      az: "Azərbaycan dilində cavab ver",
      ru: "Отвечай на русском языке",
      ar: "أجب باللغة العربية"
    };

    const instruction = languageInstructions[language as keyof typeof languageInstructions] || languageInstructions.en;

    return `You are a helpful and knowledgeable food recommendation assistant. ${instruction}.

USER PROFILE:
- Dietary Restrictions: ${context.preferences.dietaryRestrictions.join(", ") || "None"}
- Allergies: ${context.preferences.allergies.join(", ") || "None"}
- Favorite Categories: ${context.preferences.favoriteCategories.join(", ") || "None specified"}
- Calorie Goal: ${context.goals.calorieGoal || "Not specified"} calories
- Dietary Goal: ${context.goals.dietaryGoal || "Not specified"}
- Average Order Value: $${context.orderHistory.averageOrderValue || "Not available"}

RECENT ORDER HISTORY:
${recentOrders.slice(0, 3).map(order => 
  `- ${order.items?.map((item: any) => item.name).join(", ") || "Unknown items"} from ${order.restaurantName || "Unknown restaurant"}`
).join("\n") || "No recent orders"}

RELEVANT MENU OPTIONS:
${relevantItems.slice(0, 5).map((item, index) => 
  `${index + 1}. ${item.name} - ${item.restaurantName}
     Price: $${item.price}
     Description: ${item.description || "No description"}
     Calories: ${item.calories || "Not specified"}
     Category: ${item.category || "Not specified"}
     Dietary: ${item.dietary?.join(", ") || "Not specified"}`
).join("\n\n")}

USER QUESTION: "${userQuery}"

Please provide a personalized recommendation based on the user's profile and preferences. Be specific about which menu item(s) you recommend and explain why they're good choices for this user. Include the restaurant name and price in your recommendation. Keep your response conversational and helpful.`;
  }

  /**
   * Extract structured recommendations from the LLM response
   */
  private extractRecommendations(relevantItems: any[], response: string): FoodRecommendation[] {
    // For now, return the top relevant items as recommendations
    // This could be enhanced with NLP to extract specific recommendations from the response
    return relevantItems.slice(0, 3).map(item => ({
      itemId: item.id || item.itemId,
      name: item.name,
      restaurantName: item.restaurantName || "Unknown Restaurant",
      restaurantId: item.restaurantId,
      price: item.price,
      description: item.description || "",
      calories: item.calories,
      matchScore: item.matchScore || 0,
      reasoning: `Recommended based on your preferences and dietary goals`,
      category: item.category || "",
      dietary: item.dietary || [],
      allergens: item.allergens || []
    }));
  }

  /**
   * Calculate confidence score for the recommendation
   */
  private calculateConfidence(relevantItems: any[], context: UserContext): number {
    if (relevantItems.length === 0) return 0;

    const avgMatchScore = relevantItems.reduce((sum, item) => sum + (item.matchScore || 0), 0) / relevantItems.length;
    const contextCompleteness = this.calculateContextCompleteness(context);
    
    return Math.min(1, (avgMatchScore * 0.7) + (contextCompleteness * 0.3));
  }

  /**
   * Calculate how complete the user context is
   */
  private calculateContextCompleteness(context: UserContext): number {
    let score = 0;
    let maxScore = 0;

    // Check various context fields
    if (context.preferences.dietaryRestrictions.length > 0) score += 0.2;
    maxScore += 0.2;

    if (context.preferences.favoriteCategories.length > 0) score += 0.2;
    maxScore += 0.2;

    if (context.goals.calorieGoal > 0) score += 0.2;
    maxScore += 0.2;

    if (context.orderHistory.frequentItems.length > 0) score += 0.2;
    maxScore += 0.2;

    if (context.preferences.allergies.length >= 0) score += 0.2; // Even empty array shows user provided info
    maxScore += 0.2;

    return maxScore > 0 ? score / maxScore : 0;
  }
}
