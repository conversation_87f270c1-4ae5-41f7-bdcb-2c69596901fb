import { useState, useEffect } from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import {
  Utensils,
  Menu,
  User,
  LogOut,
  LayoutDashboard,
  Settings,
  Palette,
  Home,
  Building,
  Info,
  Tags,
  Award,
  Shield,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useAuth } from "@/providers/AuthProvider";
import { Separator } from "@/components/ui/separator";
import { OfflineIndicator } from "@/components/OfflineIndicator";
import { PointsBalance } from "@/components/loyalty/PointsBalance";
import { useLoyaltyStatus } from "@/lib/react-query/hooks/useLoyalty";

const navItems = [
  { title: "Home", href: "/", icon: Home },
  { title: "Restaurants", href: "/restaurants", icon: Building },
  { title: "About", href: "/about", icon: Info },
  { title: "Pricing", href: "/pricing", icon: Tags },
];

// Commented out for now as we're not using these props
// interface HeaderProps {
//   onSoundToggle?: () => void;
//   isSoundEnabled?: boolean;
// }

export const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, userRole, logOut } = useAuth();
  const navigate = useNavigate();

  // Fetch user's loyalty status for points display
  const { data: loyaltyStatus } = useLoyaltyStatus(user?.uid);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);

    handleScroll();
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const getInitials = (name?: string | null): string => {
    if (!name) return "??";
    return name
      .split(" ")
      .map((n) => n[0])
      .slice(0, 2)
      .join("")
      .toUpperCase();
  };

  const handleNavigate = (path: string) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const handleLogout = () => {
    logOut();
    setIsMobileMenuOpen(false);
  };

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300 ease-in-out",
        isScrolled
          ? "bg-background/95 backdrop-blur-sm border-b border-border/50 shadow-sm"
          : "bg-transparent border-b border-transparent"
      )}
    >
      <div className="px-4 sm:px-2 lg:px-4">
        {" "}
        {/* Use container for centering */}
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link
            to="/"
            className="flex items-center gap-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring rounded-md"
            aria-label="Go to Homepage"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              id="a"
              className="h-12 w-12"
              viewBox="0 0 442.4 442.46"
            >
              <defs>
                <linearGradient
                  id="b"
                  x1="64.75"
                  x2="418.88"
                  y1="64.8"
                  y2="418.93"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop offset="0" stopColor="#b11f24"></stop>
                  <stop offset="1" stopColor="#fff200"></stop>
                </linearGradient>
                <linearGradient
                  id="c"
                  x1="96.97"
                  x2="345.39"
                  y1="96.33"
                  y2="344.76"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop offset="0" stopColor="#b11f24"></stop>
                  <stop offset="1" stopColor="#fff200"></stop>
                </linearGradient>
                <linearGradient
                  id="d"
                  x1="128.43"
                  x2="313.01"
                  y1="128.48"
                  y2="313.06"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop offset="0" stopColor="#b11f24"></stop>
                  <stop offset="1" stopColor="#fff200"></stop>
                </linearGradient>
              </defs>
              <path
                fill="url(#b)"
                d="M408.77 338.63c21.51-34.22 33.21-73.76 33.63-115.1.6-59.02-22.52-116.08-64.06-158-50.64-51.1-121.76-74.46-194.9-62.42a213.6 213.6 0 0 0-89.81 37.21C32.37 84.27-1.53 152.64 0 225.99c1.2 57.42 24.89 112.31 65.75 152.66 41.66 41.15 96.81 63.8 155.43 63.8 45.02 0 87.99-13.36 124.37-38.2l11.79 11.79c8.6 8.61 20.04 13.35 32.21 13.35s23.61-4.74 32.21-13.35c9.44-9.43 14.22-22.26 13.22-35.73-.83-11.05-5.88-21.36-13.71-29.19l-12.5-12.5Zm-3.27 61.16c-4.87 4.87-11.58 7.25-18.58 6.46-5.18-.58-9.95-3.1-13.64-6.78l-9.28-9.28-16.4-16.4c-5.96 4.96-12.21 9.56-18.73 13.79-31.81 20.68-69.92 32.47-110.74 31.86-105.4-1.59-191.7-86.37-195.08-191.74C19.39 113.31 113.22 19.47 227.6 23.1c106.22 3.37 191.34 90.97 191.81 197.25.16 37.01-9.88 71.73-27.46 101.46a200 200 0 0 1-13.09 19.43l16.46 16.47 9.85 9.85c3.66 3.66 6.17 8.4 6.78 13.55.82 7.02-1.55 13.78-6.45 18.68"
              ></path>
              <path
                fill="url(#c)"
                d="M397.41 221.85c-.11 30.34-7.92 58.91-21.59 83.83-3.79 6.91-8.03 13.54-12.68 19.85l-16.47-16.47a153 153 0 0 0 12.1-20.43c10.66-21.67 16.35-46.21 15.57-72.12-2.44-80.76-68.05-146.2-148.81-148.45-86.47-2.41-157.58 67.24-157.58 153.17s68.74 153.23 153.23 153.23c27.2 0 52.78-7.13 74.95-19.62 6.84-3.85 13.35-8.21 19.49-13.03l16.36 16.36a178 178 0 0 1-19.05 13.47c-27.98 17.13-61.1 26.68-96.44 25.76-92.33-2.41-170.36-81.55-171.53-173.9-1.25-98.97 79.54-179.75 178.52-178.48 96.3 1.24 174.26 80.53 173.92 176.84Z"
              ></path>
              <path
                fill="url(#d)"
                d="M352.35 217.26c.58 19.44-3.1 37.99-10.18 54.77-3.13 7.44-6.93 14.53-11.32 21.2l-16.68-16.68c4.19-7 7.6-14.5 10.12-22.4 3.76-11.72 5.56-24.32 5.03-37.38-2.32-57.42-49.89-103.35-107.35-103.76-61.27-.44-111.01 50.3-108.95 111.92 1.89 56.69 47.91 102.65 104.6 104.48 15.94.52 31.15-2.43 44.92-8.15 7.44-3.09 14.46-6.98 20.96-11.57l16.45 16.45c-6.28 4.73-12.99 8.9-20.07 12.46-18.63 9.35-39.79 14.4-62.14 13.83-69.76-1.8-126.3-58.65-127.75-128.41-1.55-74.65 59.59-135.69 134.28-133.97 69.45 1.6 126.04 57.79 128.09 127.23Z"
              ></path>
            </svg>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-2 lg:gap-4">
            {navItems.map((item) => (
              <NavLink
                key={item.title}
                to={item.href}
                className={({ isActive }) =>
                  cn(
                    "rounded-md px-3 py-1.5 text-sm font-medium transition-colors hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                    isActive
                      ? "text-primary"
                      : isScrolled
                      ? "text-foreground hover:text-primary"
                      : "text-primary hover:text-primary/80",
                    !isScrolled && !isActive && "text-primary"
                  )
                }
              >
                {item.title}
              </NavLink>
            ))}
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center gap-2 md:gap-3">
            {/* Offline Indicator */}
            <div className="hidden md:block">
              <OfflineIndicator />
            </div>

            {/* User Menu / Login Button */}
            {user ? (
              <div className="flex items-center gap-3">
                {/* Points Balance for Client Users */}
                {userRole === "client" && loyaltyStatus && (
                  <PointsBalance
                    points={loyaltyStatus.totalPoints}
                    variant="outline"
                    size="sm"
                    className="hidden sm:flex"
                  />
                )}

                <DropdownMenu modal={false}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className={cn(
                        "relative h-9 w-9 rounded-full focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                        isScrolled ? "" : "hover:bg-primary/10"
                      )}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={user.photoURL || ""}
                          alt={user.displayName ?? "User Avatar"}
                        />
                        <AvatarFallback>
                          {getInitials(user.displayName || user.email)}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user.displayName || "User"}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onSelect={() => handleNavigate("/profile")}
                    >
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenuItem>
                    {userRole === "client" && (
                      <DropdownMenuItem
                        onSelect={() => handleNavigate("/loyalty")}
                      >
                        <Award className="mr-2 h-4 w-4" />
                        <span>Loyalty Program</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onSelect={() => handleNavigate("/dashboard")}
                    >
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      <span>Dashboard</span>
                    </DropdownMenuItem>
                    {userRole === "restaurant" && (
                      <DropdownMenuItem
                        onSelect={() => handleNavigate("/admin-panel")}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Admin Panel</span>
                      </DropdownMenuItem>
                    )}
                    {user.email === "<EMAIL>" && (
                      <>
                        <DropdownMenuItem
                          onSelect={() => handleNavigate("/admin")}
                        >
                          <Shield className="mr-2 h-4 w-4" />
                          <span>Admin Panel</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onSelect={() => handleNavigate("/admin-rewards")}
                        >
                          <Award className="mr-2 h-4 w-4" />
                          <span>Global Rewards</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onSelect={() => handleNavigate("/email-panel")}
                        >
                          <Palette className="mr-2 h-4 w-4" />
                          <span>Email Template</span>
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onSelect={handleLogout}
                      className="text-red-600 focus:text-red-600 focus:bg-red-50"
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <Button
                variant={isScrolled ? "outline" : "ghost"}
                size="sm"
                onClick={() => handleNavigate("/auth")}
                className={cn(
                  !isScrolled && "text-primary hover:bg-primary/10"
                )}
              >
                Login
              </Button>
            )}

            {/* Mobile Menu Trigger */}
            <Sheet
              modal={false}
              open={isMobileMenuOpen}
              onOpenChange={setIsMobileMenuOpen}
            >
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "md:hidden rounded-full",
                    isScrolled
                      ? "text-foreground hover:bg-accent"
                      : "text-primary hover:bg-primary/10"
                  )}
                  aria-label="Open main menu"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent
                side="right"
                className="w-full max-w-xs sm:max-w-sm"
                hideCloseButton
              >
                <SheetHeader className="border-b pb-4 mb-4">
                  <SheetTitle className="flex items-center gap-2">
                    <Utensils className="h-5 w-5 text-primary" />
                    <span className="text-lg font-semibold">Qonai</span>
                  </SheetTitle>
                  {/* Kapatma butonu kaldırıldı - Sheet bileşeni zaten otomatik olarak bir X butonu ekliyor */}
                </SheetHeader>

                <nav className="flex flex-col space-y-2 mb-6">
                  {navItems.map((item) => (
                    <NavLink
                      key={item.title}
                      to={item.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={({ isActive }) =>
                        cn(
                          "flex items-center gap-3 rounded-md px-3 py-2 text-base font-medium transition-colors hover:bg-accent",
                          isActive
                            ? "text-primary bg-accent"
                            : "text-muted-foreground"
                        )
                      }
                    >
                      <item.icon className="h-5 w-5" />
                      {item.title}
                    </NavLink>
                  ))}
                </nav>

                <Separator className="my-4" />

                {/* Mobile Offline Indicator */}
                <div className="mb-4 px-3">
                  <OfflineIndicator />
                </div>

                <Separator className="my-4" />

                {/* Mobile User Actions */}
                <div className="flex flex-col space-y-2">
                  {user ? (
                    <>
                      <Button
                        variant="ghost"
                        className="justify-start gap-3 px-3"
                        onClick={() => handleNavigate("/profile")}
                      >
                        <User className="h-5 w-5 text-muted-foreground" />{" "}
                        Profile
                      </Button>
                      {userRole === "client" && (
                        <Button
                          variant="ghost"
                          className="justify-start gap-3 px-3"
                          onClick={() => handleNavigate("/loyalty")}
                        >
                          <Award className="h-5 w-5 text-muted-foreground" />{" "}
                          Loyalty Program
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        className="justify-start gap-3 px-3"
                        onClick={() => handleNavigate("/dashboard")}
                      >
                        <LayoutDashboard className="h-5 w-5 text-muted-foreground" />{" "}
                        Dashboard
                      </Button>
                      {userRole === "restaurant" && (
                        <Button
                          variant="ghost"
                          className="justify-start gap-3 px-3"
                          onClick={() => handleNavigate("/admin-panel")}
                        >
                          <Settings className="h-5 w-5 text-muted-foreground" />{" "}
                          Admin Panel
                        </Button>
                      )}
                      {user.email === "<EMAIL>" && (
                        <>
                          <Button
                            variant="ghost"
                            className="justify-start gap-3 px-3"
                            onClick={() => handleNavigate("/admin")}
                          >
                            <Shield className="h-5 w-5 text-muted-foreground" />{" "}
                            Admin Panel
                          </Button>
                          <Button
                            variant="ghost"
                            className="justify-start gap-3 px-3"
                            onClick={() => handleNavigate("/admin-rewards")}
                          >
                            <Award className="h-5 w-5 text-muted-foreground" />{" "}
                            Global Rewards
                          </Button>
                          <Button
                            variant="ghost"
                            className="justify-start gap-3 px-3"
                            onClick={() => handleNavigate("/email-panel")}
                          >
                            <Palette className="h-5 w-5 text-muted-foreground" />{" "}
                            Email Template
                          </Button>
                        </>
                      )}
                      <Button
                        variant="ghost"
                        className="justify-start gap-3 px-3 text-red-600 hover:text-red-600 hover:bg-red-50"
                        onClick={handleLogout}
                      >
                        <LogOut className="h-5 w-5" /> Log out
                      </Button>
                    </>
                  ) : (
                    <Button
                      className="w-full"
                      onClick={() => handleNavigate("/auth")}
                    >
                      Login / Register
                    </Button>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};
