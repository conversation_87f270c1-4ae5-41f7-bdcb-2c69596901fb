{"name": "qonai", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@paypal/react-paypal-js": "^8.8.3", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/query-sync-storage-persister": "^5.76.1", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@tanstack/react-query-persist-client": "^5.76.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "firebase": "^11.7.3", "framer-motion": "^12.12.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-geolocated": "^4.3.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-markdown": "^8.0.7", "react-quill": "^2.0.0", "react-rnd": "^10.5.2", "react-router-dom": "^7.6.0", "react-share": "^5.2.2", "react-window": "^1.8.11", "recharts": "^2.15.3", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/types": "^7.27.1", "@eslint/js": "^9.27.0", "@tanstack/eslint-plugin-query": "^5.74.7", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@types/babel-generator": "^6.25.8", "@types/estree": "^1.0.7", "@types/file-saver": "^2.0.7", "@types/geojson": "^7946.0.16", "@types/json-schema": "^7.0.15", "@types/leaflet": "^1.9.17", "@types/lodash": "^4.17.16", "@types/node": "^22.15.18", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "react-dev-inspector": "^2.0.1", "shadcn-ui": "^0.9.5", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}, "description": "AI based digital restaurants platform.", "main": "config.js", "repository": {"type": "git", "url": "git+https://github.com/alyvdev/Qonai.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/alyvdev/Qonai/issues"}, "homepage": "https://github.com/alyvdev/Qonai#readme"}