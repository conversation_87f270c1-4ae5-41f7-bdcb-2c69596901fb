import { firestore } from "@/config/firebase";
import {
  doc,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
} from "firebase/firestore";
import { UserContext } from "./RAGService";

/**
 * Service for managing user context and preferences
 * This service aggregates user data from various sources to build a comprehensive
 * user profile for personalized recommendations
 */
export class UserContextService {
  constructor() {
    // Initialize the service
  }

  /**
   * Get comprehensive user context for RAG recommendations
   */
  async getUserContext(userId: string): Promise<UserContext> {
    try {
      // Fetch user profile data
      const userProfile = await this.getUserProfile(userId);

      // Fetch order history
      const orderHistory = await this.getOrderHistory(userId);

      // Fetch user preferences
      const preferences = await this.getUserPreferences(userId);

      // Fetch user goals
      const goals = await this.getUserGoals(userId);

      // Construct comprehensive context
      const context: UserContext = {
        preferences: {
          favoriteCategories: preferences.favoriteCategories || [],
          dietaryRestrictions: preferences.dietaryRestrictions || [],
          allergies: preferences.allergies || [],
          dislikedIngredients: preferences.dislikedIngredients || [],
          preferredCuisines: preferences.preferredCuisines || [],
          spiceLevel: preferences.spiceLevel || "medium",
        },
        goals: {
          calorieGoal: goals.dailyCalorieGoal || 2000,
          dietaryGoal: goals.goal || "maintain",
          healthGoals: goals.healthGoals || [],
        },
        orderHistory: {
          frequentItems: orderHistory.frequentItems || [],
          frequentRestaurants: orderHistory.frequentRestaurants || [],
          averageOrderValue: orderHistory.averageOrderValue || 25,
          lastOrderDate: orderHistory.lastOrderDate || new Date(),
        },
        location: userProfile.location,
      };

      return context;
    } catch (error) {
      console.error("Error getting user context:", error);
      // Return default context if there's an error
      return this.getDefaultUserContext();
    }
  }

  /**
   * Get user profile data from Firestore
   */
  private async getUserProfile(userId: string): Promise<any> {
    try {
      const userRef = doc(firestore, "clients", userId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        return userDoc.data();
      }

      return {};
    } catch (error) {
      console.error("Error fetching user profile:", error);
      return {};
    }
  }

  /**
   * Get user preferences from profile
   */
  private async getUserPreferences(userId: string): Promise<any> {
    try {
      const userProfile = await this.getUserProfile(userId);
      const mealPreferences = userProfile.mealPreferences || {};

      return {
        favoriteCategories: mealPreferences.preferredCategories || [],
        dietaryRestrictions: mealPreferences.dietaryRestrictions || [],
        allergies: mealPreferences.allergies || [],
        dislikedIngredients: mealPreferences.dislikedIngredients || [],
        preferredCuisines: mealPreferences.preferredCuisines || [],
        spiceLevel: mealPreferences.spiceLevel || "medium",
        favoriteIngredients: mealPreferences.favoriteIngredients || [],
      };
    } catch (error) {
      console.error("Error fetching user preferences:", error);
      return {};
    }
  }

  /**
   * Get user dietary and health goals
   */
  private async getUserGoals(userId: string): Promise<any> {
    try {
      const userProfile = await this.getUserProfile(userId);
      const calorieCalculator = userProfile.calorieCalculator || {};
      const dietaryGoals = userProfile.dietaryGoals || [];

      return {
        dailyCalorieGoal: calorieCalculator.dailyCalorieGoal || 2000,
        goal: calorieCalculator.goal || "maintain", // "lose", "gain", "maintain"
        activityLevel: calorieCalculator.activityLevel || "moderate",
        healthGoals: dietaryGoals.map((goal: any) => goal.name || goal),
      };
    } catch (error) {
      console.error("Error fetching user goals:", error);
      return {
        dailyCalorieGoal: 2000,
        goal: "maintain",
        activityLevel: "moderate",
        healthGoals: [],
      };
    }
  }

  /**
   * Analyze user's order history to extract patterns
   */
  private async getOrderHistory(userId: string): Promise<any> {
    try {
      // Fetch recent orders
      const ordersRef = collection(firestore, "orders");
      const ordersQuery = query(
        ordersRef,
        where("userId", "==", userId),
        orderBy("createdAt", "desc"),
        limit(20) // Get last 20 orders for analysis
      );

      const ordersSnapshot = await getDocs(ordersQuery);
      const orders = ordersSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      if (orders.length === 0) {
        return {
          frequentItems: [],
          frequentRestaurants: [],
          averageOrderValue: 25,
          lastOrderDate: new Date(),
        };
      }

      // Analyze frequent items
      const itemFrequency: { [key: string]: number } = {};
      const restaurantFrequency: { [key: string]: number } = {};
      let totalValue = 0;

      orders.forEach((order: any) => {
        // Count restaurant frequency
        if (order.restaurantId) {
          restaurantFrequency[order.restaurantId] =
            (restaurantFrequency[order.restaurantId] || 0) + 1;
        }

        // Count item frequency and calculate total value
        if (order.items && Array.isArray(order.items)) {
          order.items.forEach((item: any) => {
            const itemKey = `${item.name}_${order.restaurantId}`;
            itemFrequency[itemKey] =
              (itemFrequency[itemKey] || 0) + item.quantity;
          });
        }

        if (order.total || order.totalPrice || order.totalAmount) {
          totalValue += order.total || order.totalPrice || order.totalAmount;
        }
      });

      // Get most frequent items and restaurants
      const frequentItems = Object.entries(itemFrequency)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([item]) => item.split("_")[0]); // Extract item name

      const frequentRestaurants = Object.entries(restaurantFrequency)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([restaurantId]) => restaurantId);

      const averageOrderValue =
        orders.length > 0 ? totalValue / orders.length : 25;
      const lastOrderDate = orders[0]?.createdAt?.toDate() || new Date();

      return {
        frequentItems,
        frequentRestaurants,
        averageOrderValue: Math.round(averageOrderValue * 100) / 100,
        lastOrderDate,
      };
    } catch (error) {
      console.error("Error analyzing order history:", error);
      return {
        frequentItems: [],
        frequentRestaurants: [],
        averageOrderValue: 25,
        lastOrderDate: new Date(),
      };
    }
  }

  /**
   * Get user's favorite restaurants based on order frequency and ratings
   */
  async getFavoriteRestaurants(
    userId: string,
    limit: number = 5
  ): Promise<string[]> {
    try {
      const orderHistory = await this.getOrderHistory(userId);
      return orderHistory.frequentRestaurants.slice(0, limit);
    } catch (error) {
      console.error("Error getting favorite restaurants:", error);
      return [];
    }
  }

  /**
   * Get user's dietary restrictions and allergies
   */
  async getDietaryRestrictions(userId: string): Promise<{
    restrictions: string[];
    allergies: string[];
  }> {
    try {
      const preferences = await this.getUserPreferences(userId);
      return {
        restrictions: preferences.dietaryRestrictions || [],
        allergies: preferences.allergies || [],
      };
    } catch (error) {
      console.error("Error getting dietary restrictions:", error);
      return {
        restrictions: [],
        allergies: [],
      };
    }
  }

  /**
   * Check if a menu item is suitable for the user based on their restrictions
   */
  async isItemSuitableForUser(userId: string, menuItem: any): Promise<boolean> {
    try {
      const { restrictions, allergies } = await this.getDietaryRestrictions(
        userId
      );

      // Check allergies
      if (menuItem.allergens && allergies.length > 0) {
        const hasAllergen = menuItem.allergens.some((allergen: string) =>
          allergies.includes(allergen)
        );
        if (hasAllergen) return false;
      }

      // Check dietary restrictions
      if (restrictions.length > 0) {
        // If user has dietary restrictions, the item should match at least one
        if (menuItem.dietary && menuItem.dietary.length > 0) {
          const matchesDiet = menuItem.dietary.some((diet: string) =>
            restrictions.includes(diet)
          );
          if (!matchesDiet) return false;
        }
      }

      return true;
    } catch (error) {
      console.error("Error checking item suitability:", error);
      return true; // Default to suitable if there's an error
    }
  }

  /**
   * Get default user context for users without profile data
   */
  private getDefaultUserContext(): UserContext {
    return {
      preferences: {
        favoriteCategories: [],
        dietaryRestrictions: [],
        allergies: [],
        dislikedIngredients: [],
        preferredCuisines: [],
        spiceLevel: "medium",
      },
      goals: {
        calorieGoal: 2000,
        dietaryGoal: "maintain",
        healthGoals: [],
      },
      orderHistory: {
        frequentItems: [],
        frequentRestaurants: [],
        averageOrderValue: 25,
        lastOrderDate: new Date(),
      },
    };
  }

  /**
   * Update user context based on new order
   */
  async updateContextFromOrder(userId: string, order: any): Promise<void> {
    try {
      // This could be used to update user preferences based on their ordering behavior
      // For now, we'll just log the update
      console.log(
        `Updating context for user ${userId} based on new order ${order.id}`
      );

      // In a full implementation, this could:
      // 1. Update favorite categories based on ordered items
      // 2. Adjust calorie goals based on ordering patterns
      // 3. Learn new preferences from item selections
      // 4. Update average order value
    } catch (error) {
      console.error("Error updating user context from order:", error);
    }
  }

  /**
   * Get user's current location if available
   */
  async getUserLocation(
    userId: string
  ): Promise<{ latitude: number; longitude: number } | null> {
    try {
      const userProfile = await this.getUserProfile(userId);
      return userProfile.location || null;
    } catch (error) {
      console.error("Error getting user location:", error);
      return null;
    }
  }
}
