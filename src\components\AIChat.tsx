import { useState, useRef, useEffect } from "react";
import { useAuth } from "@/providers/AuthProvider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, Send, Bot, User, Globe } from "lucide-react";
import { toast } from "sonner";
import { ComprehensiveRAGService } from "@/services/ComprehensiveRAGService";

interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  language?: string;
  queryType?: "menu" | "restaurant" | "reservation" | "review" | "general";
  data?: {
    menuItems?: any[];
    restaurants?: any[];
    reservations?: any[];
    reviews?: any[];
    orders?: any[];
  };
  confidence?: number;
  sources?: string[];
}

export const AIChat = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("en");
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const ragService = new ComprehensiveRAGService();

  // Supported languages
  const supportedLanguages = [
    { code: "en", name: "English", flag: "🇺🇸" },
    { code: "tr", name: "Türkçe", flag: "🇹🇷" },
    { code: "az", name: "Azərbaycan", flag: "🇦🇿" },
    { code: "ru", name: "Русский", flag: "🇷🇺" },
    { code: "ar", name: "العربية", flag: "🇸🇦" },
  ];

  useEffect(() => {
    // Initialize chat with welcome message
    if (messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: Date.now().toString(),
        role: "assistant",
        content: getWelcomeMessage(selectedLanguage),
        timestamp: new Date(),
        language: selectedLanguage,
      };
      setMessages([welcomeMessage]);
    }
  }, [selectedLanguage, messages.length]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages are added
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const getWelcomeMessage = (language: string): string => {
    const welcomeMessages = {
      en: "Hello! I'm your AI food assistant. I can help you find personalized meal recommendations based on your preferences, dietary needs, and goals. What would you like to eat today?",
      tr: "Merhaba! Ben sizin AI yemek asistanınızım. Tercihleriniz, diyet ihtiyaçlarınız ve hedeflerinize göre kişiselleştirilmiş yemek önerileri bulmanıza yardımcı olabilirim. Bugün ne yemek istiyorsunuz?",
      az: "Salam! Mən sizin AI yemək köməkçinizəm. Zövqləriniz, pəhriz ehtiyaclarınız və məqsədlərinizə görə fərdiləşdirilmiş yemək tövsiyələri tapmağa kömək edə bilərəm. Bu gün nə yemək istəyirsiniz?",
      ru: "Привет! Я ваш ИИ-помощник по еде. Я могу помочь вам найти персонализированные рекомендации блюд на основе ваших предпочтений, диетических потребностей и целей. Что бы вы хотели съесть сегодня?",
      ar: "مرحبا! أنا مساعد الطعام الذكي الخاص بك. يمكنني مساعدتك في العثور على توصيات وجبات مخصصة بناءً على تفضيلاتك واحتياجاتك الغذائية وأهدافك. ماذا تريد أن تأكل اليوم؟",
    };
    return (
      welcomeMessages[language as keyof typeof welcomeMessages] ||
      welcomeMessages.en
    );
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !user) {
      toast.error("Please enter a message and make sure you're logged in.");
      return;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: "user",
      content: inputValue.trim(),
      timestamp: new Date(),
      language: selectedLanguage,
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);

    try {
      // Generate response using comprehensive RAG
      const ragResponse = await ragService.generateResponse(
        inputValue.trim(),
        user.uid,
        selectedLanguage
      );

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: ragResponse.response,
        timestamp: new Date(),
        language: selectedLanguage,
        queryType: ragResponse.queryType,
        data: ragResponse.data,
        confidence: ragResponse.confidence,
        sources: ragResponse.sources,
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Error generating AI response:", error);
      toast.error("Sorry, I couldn't process your request. Please try again.");

      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content:
          "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.",
        timestamp: new Date(),
        language: selectedLanguage,
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  if (!user) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">
            Please log in to use the AI food assistant.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto h-[600px] flex flex-col">
      <CardHeader className="flex-shrink-0 border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI Food Assistant
          </CardTitle>
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              className="text-sm border rounded px-2 py-1"
            >
              {supportedLanguages.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.flag} {lang.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.role === "user"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  }`}
                >
                  <div className="flex items-start gap-2">
                    {message.role === "assistant" && (
                      <Bot className="h-4 w-4 mt-1 flex-shrink-0" />
                    )}
                    {message.role === "user" && (
                      <User className="h-4 w-4 mt-1 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      <p className="text-sm whitespace-pre-wrap">
                        {message.content}
                      </p>
                      {/* Show relevant data based on query type */}
                      {message.data && (
                        <div className="mt-3 space-y-2">
                          {/* Menu Items */}
                          {message.data.menuItems &&
                            message.data.menuItems.length > 0 && (
                              <div>
                                <p className="text-xs font-medium opacity-80 mb-2">
                                  🍽️ Menü Önerileri:
                                </p>
                                {message.data.menuItems
                                  .slice(0, 3)
                                  .map((item: any, index: number) => (
                                    <div
                                      key={item.id || index}
                                      className="bg-background/50 rounded p-2 text-xs mb-1"
                                    >
                                      <div className="font-medium">
                                        {item.name}
                                      </div>
                                      <div className="text-muted-foreground">
                                        {item.restaurantName} • $
                                        {item.price || "Fiyat yok"}
                                      </div>
                                      {item.description && (
                                        <div className="mt-1 text-xs opacity-60">
                                          {item.description.slice(0, 100)}...
                                        </div>
                                      )}
                                    </div>
                                  ))}
                              </div>
                            )}

                          {/* Restaurants */}
                          {message.data.restaurants &&
                            message.data.restaurants.length > 0 && (
                              <div>
                                <p className="text-xs font-medium opacity-80 mb-2">
                                  🏪 Restoranlar:
                                </p>
                                {message.data.restaurants
                                  .slice(0, 3)
                                  .map((restaurant: any, index: number) => (
                                    <div
                                      key={restaurant.id || index}
                                      className="bg-background/50 rounded p-2 text-xs mb-1"
                                    >
                                      <div className="font-medium">
                                        {restaurant.restaurantName}
                                      </div>
                                      <div className="text-muted-foreground">
                                        {restaurant.cuisines?.join(", ") ||
                                          "Mutfak bilgisi yok"}
                                      </div>
                                      {restaurant.rating && (
                                        <div className="mt-1 text-xs opacity-60">
                                          ⭐ {restaurant.rating}/5
                                        </div>
                                      )}
                                    </div>
                                  ))}
                              </div>
                            )}

                          {/* Query Type & Confidence */}
                          {message.queryType && (
                            <div className="mt-2 pt-2 border-t border-border/20">
                              <div className="flex items-center justify-between text-xs opacity-60">
                                <span>Soru Tipi: {message.queryType}</span>
                                {message.confidence && (
                                  <span>
                                    Güven:{" "}
                                    {Math.round(message.confidence * 100)}%
                                  </span>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                      <p className="text-xs opacity-50 mt-1">
                        {formatTime(message.timestamp)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-muted rounded-lg p-3 flex items-center gap-2">
                  <Bot className="h-4 w-4" />
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        <div className="border-t p-4">
          <div className="flex gap-2">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Menü, restoran, rezervasyon hakkında soru sorun..."
              disabled={isLoading}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={isLoading || !inputValue.trim()}
              size="icon"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
