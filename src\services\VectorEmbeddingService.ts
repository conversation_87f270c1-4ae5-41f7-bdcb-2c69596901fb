import { firestore } from "@/config/firebase";
import { getDoc, doc, setDoc, serverTimestamp } from "firebase/firestore";
// Firebase imports removed as they're not currently used in this implementation

export interface EmbeddingResult {
  embedding: number[];
  text: string;
  timestamp: Date;
}

export interface MenuItemEmbedding {
  itemId: string;
  restaurantId: string;
  name: string;
  description: string;
  embedding: number[];
  metadata: {
    price: number;
    calories?: number;
    category: string;
    dietary: string[];
    allergens?: string[];
    lastUpdated: Date;
  };
}

export interface UserProfileEmbedding {
  userId: string;
  profileText: string;
  embedding: number[];
  metadata: {
    preferences: string[];
    restrictions: string[];
    goals: string[];
    lastUpdated: Date;
  };
}

/**
 * Service for handling vector embeddings using Google's embedding API
 * This service manages the creation, storage, and retrieval of vector embeddings
 * for menu items and user profiles to enable semantic search in the RAG system
 */
export class VectorEmbeddingService {
  // private readonly EMBEDDING_MODEL = "text-embedding-004"; // Google's latest embedding model
  private readonly EMBEDDING_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  constructor() {
    // Initialize the service
  }

  /**
   * Generate embedding for a given text using semantic similarity
   * Since we don't have vector embeddings set up yet, we'll use text-based similarity
   */
  async generateEmbedding(text: string): Promise<number[]> {
    try {
      // For now, we'll create a simple text-based embedding
      // This will be replaced with actual Google Vertex AI embedding API when available
      return this.generateMockEmbedding(text);
    } catch (error) {
      console.error("Error generating embedding:", error);
      throw new Error("Failed to generate embedding");
    }
  }

  /**
   * Generate mock embedding for development/testing
   * This should be replaced with actual API calls in production
   */
  private generateMockEmbedding(text: string): number[] {
    // Generate a deterministic mock embedding based on text content
    const dimension = 768; // Standard dimension for text-embedding models
    const embedding: number[] = [];

    // Use text hash to generate consistent embeddings for same text
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Generate embedding values based on hash
    for (let i = 0; i < dimension; i++) {
      const seed = hash + i;
      const value = (Math.sin(seed) * 10000) % 1;
      embedding.push(value);
    }

    // Normalize the vector
    const magnitude = Math.sqrt(
      embedding.reduce((sum, val) => sum + val * val, 0)
    );
    return embedding.map((val) => val / magnitude);
  }

  /**
   * Create embedding for a menu item
   */
  async createMenuItemEmbedding(menuItem: any): Promise<MenuItemEmbedding> {
    try {
      // Construct descriptive text for the menu item
      const descriptiveText = this.constructMenuItemText(menuItem);

      // Generate embedding
      const embedding = await this.generateEmbedding(descriptiveText);

      const menuItemEmbedding: MenuItemEmbedding = {
        itemId: menuItem.itemId || menuItem.id,
        restaurantId: menuItem.restaurantId,
        name: menuItem.name,
        description: menuItem.description || "",
        embedding,
        metadata: {
          price: menuItem.price,
          calories: menuItem.calories,
          category: menuItem.category,
          dietary: menuItem.dietary || [],
          allergens: menuItem.allergens || [],
          lastUpdated: new Date(),
        },
      };

      // Store in Firestore
      await this.storeMenuItemEmbedding(menuItemEmbedding);

      return menuItemEmbedding;
    } catch (error) {
      console.error("Error creating menu item embedding:", error);
      throw error;
    }
  }

  /**
   * Create embedding for user profile
   */
  async createUserProfileEmbedding(
    userId: string,
    userProfile: any
  ): Promise<UserProfileEmbedding> {
    try {
      // Construct descriptive text for the user profile
      const profileText = this.constructUserProfileText(userProfile);

      // Generate embedding
      const embedding = await this.generateEmbedding(profileText);

      const userEmbedding: UserProfileEmbedding = {
        userId,
        profileText,
        embedding,
        metadata: {
          preferences: userProfile.mealPreferences?.favoriteIngredients || [],
          restrictions: userProfile.mealPreferences?.dietaryRestrictions || [],
          goals: userProfile.calorieCalculator
            ? [userProfile.calorieCalculator.goal]
            : [],
          lastUpdated: new Date(),
        },
      };

      // Store in Firestore
      await this.storeUserProfileEmbedding(userEmbedding);

      return userEmbedding;
    } catch (error) {
      console.error("Error creating user profile embedding:", error);
      throw error;
    }
  }

  /**
   * Construct descriptive text for menu item embedding
   */
  private constructMenuItemText(menuItem: any): string {
    const parts = [];

    // Basic info
    parts.push(`Name: ${menuItem.name}`);

    if (menuItem.description) {
      parts.push(`Description: ${menuItem.description}`);
    }

    if (menuItem.category) {
      parts.push(`Category: ${menuItem.category}`);
    }

    // Nutritional info
    if (menuItem.calories) {
      parts.push(`Calories: ${menuItem.calories}`);
    }

    if (menuItem.protein) {
      parts.push(`Protein: ${menuItem.protein}g`);
    }

    if (menuItem.carbs) {
      parts.push(`Carbohydrates: ${menuItem.carbs}g`);
    }

    if (menuItem.fat) {
      parts.push(`Fat: ${menuItem.fat}g`);
    }

    // Dietary information
    if (menuItem.dietary && menuItem.dietary.length > 0) {
      parts.push(`Dietary options: ${menuItem.dietary.join(", ")}`);
    }

    if (menuItem.allergens && menuItem.allergens.length > 0) {
      parts.push(`Contains allergens: ${menuItem.allergens.join(", ")}`);
    }

    if (menuItem.ingredients && menuItem.ingredients.length > 0) {
      parts.push(`Key ingredients: ${menuItem.ingredients.join(", ")}`);
    }

    // Spice level
    if (menuItem.spicyLevel && menuItem.spicyLevel !== "none") {
      parts.push(`Spice level: ${menuItem.spicyLevel}`);
    }

    // Price
    parts.push(`Price: $${menuItem.price}`);

    // Restaurant context
    if (menuItem.restaurantName) {
      parts.push(`Restaurant: ${menuItem.restaurantName}`);
    }

    return parts.join(". ");
  }

  /**
   * Construct descriptive text for user profile embedding
   */
  private constructUserProfileText(userProfile: any): string {
    const parts = [];

    // Basic preferences
    if (userProfile.mealPreferences) {
      const prefs = userProfile.mealPreferences;

      if (prefs.favoriteIngredients && prefs.favoriteIngredients.length > 0) {
        parts.push(
          `Favorite ingredients: ${prefs.favoriteIngredients.join(", ")}`
        );
      }

      if (prefs.dislikedIngredients && prefs.dislikedIngredients.length > 0) {
        parts.push(`Dislikes: ${prefs.dislikedIngredients.join(", ")}`);
      }

      if (prefs.dietaryRestrictions && prefs.dietaryRestrictions.length > 0) {
        parts.push(
          `Dietary restrictions: ${prefs.dietaryRestrictions.join(", ")}`
        );
      }

      if (prefs.allergies && prefs.allergies.length > 0) {
        parts.push(`Allergies: ${prefs.allergies.join(", ")}`);
      }

      if (prefs.preferredCuisines && prefs.preferredCuisines.length > 0) {
        parts.push(`Preferred cuisines: ${prefs.preferredCuisines.join(", ")}`);
      }

      if (prefs.preferredCategories && prefs.preferredCategories.length > 0) {
        parts.push(
          `Preferred food categories: ${prefs.preferredCategories.join(", ")}`
        );
      }

      if (prefs.spiceLevel) {
        parts.push(`Preferred spice level: ${prefs.spiceLevel}`);
      }
    }

    // Calorie and health goals
    if (userProfile.calorieCalculator) {
      const calc = userProfile.calorieCalculator;

      if (calc.goal) {
        parts.push(`Health goal: ${calc.goal} weight`);
      }

      if (calc.dailyCalorieGoal) {
        parts.push(`Daily calorie target: ${calc.dailyCalorieGoal} calories`);
      }

      if (calc.activityLevel) {
        parts.push(`Activity level: ${calc.activityLevel}`);
      }
    }

    // Dietary goals
    if (userProfile.dietaryGoals && userProfile.dietaryGoals.length > 0) {
      const goals = userProfile.dietaryGoals
        .map((goal: any) => goal.name || goal)
        .join(", ");
      parts.push(`Dietary goals: ${goals}`);
    }

    return parts.join(". ");
  }

  /**
   * Store menu item embedding in Firestore
   */
  private async storeMenuItemEmbedding(
    embedding: MenuItemEmbedding
  ): Promise<void> {
    try {
      const embeddingRef = doc(
        firestore,
        "embeddings",
        "menuItems",
        "items",
        embedding.itemId
      );

      await setDoc(embeddingRef, {
        ...embedding,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error("Error storing menu item embedding:", error);
      throw error;
    }
  }

  /**
   * Store user profile embedding in Firestore
   */
  private async storeUserProfileEmbedding(
    embedding: UserProfileEmbedding
  ): Promise<void> {
    try {
      const embeddingRef = doc(
        firestore,
        "embeddings",
        "userProfiles",
        "profiles",
        embedding.userId
      );

      await setDoc(embeddingRef, {
        ...embedding,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error("Error storing user profile embedding:", error);
      throw error;
    }
  }

  /**
   * Get cached embedding for a menu item
   */
  async getMenuItemEmbedding(
    itemId: string
  ): Promise<MenuItemEmbedding | null> {
    try {
      const embeddingRef = doc(
        firestore,
        "embeddings",
        "menuItems",
        "items",
        itemId
      );

      const embeddingDoc = await getDoc(embeddingRef);

      if (embeddingDoc.exists()) {
        const data = embeddingDoc.data();

        // Check if embedding is still fresh
        const lastUpdated = data.metadata.lastUpdated.toDate();
        const now = new Date();

        if (
          now.getTime() - lastUpdated.getTime() <
          this.EMBEDDING_CACHE_DURATION
        ) {
          return data as MenuItemEmbedding;
        }
      }

      return null;
    } catch (error) {
      console.error("Error getting menu item embedding:", error);
      return null;
    }
  }

  /**
   * Get cached embedding for a user profile
   */
  async getUserProfileEmbedding(
    userId: string
  ): Promise<UserProfileEmbedding | null> {
    try {
      const embeddingRef = doc(
        firestore,
        "embeddings",
        "userProfiles",
        "profiles",
        userId
      );

      const embeddingDoc = await getDoc(embeddingRef);

      if (embeddingDoc.exists()) {
        const data = embeddingDoc.data();

        // Check if embedding is still fresh
        const lastUpdated = data.metadata.lastUpdated.toDate();
        const now = new Date();

        if (
          now.getTime() - lastUpdated.getTime() <
          this.EMBEDDING_CACHE_DURATION
        ) {
          return data as UserProfileEmbedding;
        }
      }

      return null;
    } catch (error) {
      console.error("Error getting user profile embedding:", error);
      return null;
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error("Vectors must have the same dimension");
    }

    let dotProduct = 0;
    let magnitudeA = 0;
    let magnitudeB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      magnitudeA += vectorA[i] * vectorA[i];
      magnitudeB += vectorB[i] * vectorB[i];
    }

    magnitudeA = Math.sqrt(magnitudeA);
    magnitudeB = Math.sqrt(magnitudeB);

    if (magnitudeA === 0 || magnitudeB === 0) {
      return 0;
    }

    return dotProduct / (magnitudeA * magnitudeB);
  }
}
