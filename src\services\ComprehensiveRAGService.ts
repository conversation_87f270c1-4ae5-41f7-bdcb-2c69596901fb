import { firestore, model } from "@/config/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  collectionGroup,
  orderBy,
  limit,
} from "firebase/firestore";
import { UserContextService } from "./UserContextService";

export interface ComprehensiveRAGResponse {
  response: string;
  queryType: "menu" | "restaurant" | "reservation" | "review" | "general";
  data: {
    menuItems?: any[];
    restaurants?: any[];
    reservations?: any[];
    reviews?: any[];
    orders?: any[];
  };
  confidence: number;
  sources: string[];
}

export class ComprehensiveRAGService {
  private contextService: UserContextService;

  constructor() {
    this.contextService = new UserContextService();
  }

  /**
   * Main method to handle any type of query
   */
  async generateResponse(
    userQuery: string,
    userId: string,
    language: string = "en"
  ): Promise<ComprehensiveRAGResponse> {
    try {
      console.log(`Processing query: "${userQuery}"`);

      // 1. Analyze query type
      const queryType = this.analyzeQueryType(userQuery);
      console.log(`Detected query type: ${queryType}`);

      // 2. Get user context
      const userContext = await this.contextService.getUserContext(userId);

      // 3. Gather relevant data based on query type
      const relevantData = await this.gatherRelevantData(
        userQuery,
        queryType,
        userContext,
        userId
      );

      // 4. Generate comprehensive prompt
      const prompt = this.buildComprehensivePrompt(
        userQuery,
        queryType,
        relevantData,
        userContext,
        language
      );

      // 5. Get AI response
      const result = await model.generateContent(prompt);
      const response = result.response.text();

      return {
        response,
        queryType,
        data: relevantData,
        confidence: this.calculateConfidence(relevantData, queryType),
        sources: this.extractSources(relevantData),
      };
    } catch (error) {
      console.error("Error in comprehensive RAG service:", error);
      throw new Error("Failed to generate response");
    }
  }

  /**
   * Analyze query to determine intent and type
   */
  private analyzeQueryType(
    userQuery: string
  ): "menu" | "restaurant" | "reservation" | "review" | "general" {
    const query = userQuery.toLowerCase();

    // Define keyword patterns for each category
    const patterns = {
      menu: [
        "food",
        "meal",
        "eat",
        "hungry",
        "menu",
        "dish",
        "recipe",
        "ingredient",
        "calories",
        "protein",
        "vegan",
        "vegetarian",
        "gluten",
        "dairy",
        "halal",
        "spicy",
        "sweet",
        "salty",
        "recommend",
        "suggestion",
        "order",
        "taste",
        "breakfast",
        "lunch",
        "dinner",
        "snack",
        "appetizer",
        "dessert",
        "main course",
        "drink",
        "beverage",
        "pizza",
        "burger",
        "salad",
        "soup",
        "pasta",
        "rice",
        "chicken",
        "beef",
        "fish",
        "seafood",
        "price",
        "cheap",
        "expensive",
      ],
      restaurant: [
        "restaurant",
        "place",
        "location",
        "address",
        "phone",
        "contact",
        "hours",
        "open",
        "close",
        "cuisine",
        "atmosphere",
        "ambiance",
        "environment",
        "delivery",
        "takeout",
        "dine in",
        "parking",
        "wifi",
        "payment",
        "card",
        "cash",
        "outdoor",
        "indoor",
        "family",
        "romantic",
        "business",
        "casual",
      ],
      reservation: [
        "reservation",
        "book",
        "table",
        "available",
        "slot",
        "time",
        "schedule",
        "tonight",
        "tomorrow",
        "weekend",
        "party",
        "group",
        "capacity",
        "seat",
        "cancel",
        "modify",
        "change",
        "confirm",
        "when",
        "what time",
      ],
      review: [
        "review",
        "rating",
        "opinion",
        "experience",
        "quality",
        "service",
        "staff",
        "good",
        "bad",
        "excellent",
        "terrible",
        "amazing",
        "awful",
        "worth",
        "feedback",
        "comment",
        "recommend",
        "satisfied",
        "disappointed",
      ],
    };

    // Calculate scores for each category
    const scores = Object.entries(patterns).reduce(
      (acc, [category, keywords]) => {
        const matches = keywords.filter((keyword) =>
          query.includes(keyword)
        ).length;
        acc[category] = matches;
        return acc;
      },
      {} as Record<string, number>
    );

    // Find the category with highest score
    const maxScore = Math.max(...Object.values(scores));

    if (maxScore === 0) {
      return "general";
    }

    const topCategory = Object.entries(scores).find(
      ([_, score]) => score === maxScore
    )?.[0];
    return (topCategory as any) || "general";
  }

  /**
   * Gather relevant data based on query type
   */
  private async gatherRelevantData(
    userQuery: string,
    queryType: string,
    userContext: any,
    userId: string
  ): Promise<any> {
    const data: any = {};

    try {
      switch (queryType) {
        case "menu":
          data.menuItems = await this.getRelevantMenuItems(userQuery);
          data.restaurants = await this.getRestaurantsForMenuItems(
            data.menuItems
          );
          break;

        case "restaurant":
          data.restaurants = await this.getRelevantRestaurants(userQuery);
          data.menuItems = await this.getPopularMenuItems(data.restaurants);
          break;

        case "reservation":
          data.restaurants = await this.getRelevantRestaurants(userQuery);
          data.reservations = await this.getReservationInfo(data.restaurants);
          break;

        case "review":
          data.restaurants = await this.getRelevantRestaurants(userQuery);
          data.reviews = await this.getRestaurantReviews(data.restaurants);
          break;

        case "general":
          // For general queries, get a bit of everything
          data.restaurants = await this.getAllRestaurants();
          data.menuItems = await this.getPopularMenuItems(
            data.restaurants.slice(0, 3)
          );
          data.orders = await this.getUserRecentOrders(userId);
          break;
      }

      return data;
    } catch (error) {
      console.error("Error gathering relevant data:", error);
      return {};
    }
  }

  /**
   * Get relevant menu items based on query
   */
  private async getRelevantMenuItems(userQuery: string): Promise<any[]> {
    try {
      const menuItemsRef = collectionGroup(firestore, "menu");
      const menuQuery = query(
        menuItemsRef,
        where("available", "==", true),
        limit(20)
      );

      const snapshot = await getDocs(menuQuery);
      const items = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        restaurantId: doc.ref.parent.parent?.id,
      }));

      // Simple text matching for now
      const searchQuery = userQuery.toLowerCase();
      return items
        .filter(
          (item) =>
            item.name?.toLowerCase().includes(searchQuery) ||
            item.description?.toLowerCase().includes(searchQuery) ||
            item.category?.toLowerCase().includes(searchQuery) ||
            item.ingredients?.some((ing: string) =>
              ing.toLowerCase().includes(searchQuery)
            )
        )
        .slice(0, 10);
    } catch (error) {
      console.error("Error getting menu items:", error);
      return [];
    }
  }

  /**
   * Get relevant restaurants based on query
   */
  private async getRelevantRestaurants(userQuery: string): Promise<any[]> {
    try {
      const restaurantsRef = collection(firestore, "restaurants");
      const snapshot = await getDocs(restaurantsRef);

      const restaurants = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Simple text matching
      const searchQuery = userQuery.toLowerCase();
      return restaurants
        .filter(
          (restaurant: any) =>
            restaurant.restaurantName?.toLowerCase().includes(searchQuery) ||
            restaurant.description?.toLowerCase().includes(searchQuery) ||
            restaurant.cuisines?.some((cuisine: string) =>
              cuisine.toLowerCase().includes(searchQuery)
            ) ||
            restaurant.categories?.some((cat: string) =>
              cat.toLowerCase().includes(searchQuery)
            )
        )
        .slice(0, 5);
    } catch (error) {
      console.error("Error getting restaurants:", error);
      return [];
    }
  }

  /**
   * Get all restaurants for general queries
   */
  private async getAllRestaurants(): Promise<any[]> {
    try {
      const restaurantsRef = collection(firestore, "restaurants");
      const snapshot = await getDocs(query(restaurantsRef, limit(10)));

      return snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
    } catch (error) {
      console.error("Error getting all restaurants:", error);
      return [];
    }
  }

  /**
   * Get popular menu items for given restaurants
   */
  private async getPopularMenuItems(restaurants: any[]): Promise<any[]> {
    if (!restaurants.length) return [];

    try {
      const allItems: any[] = [];

      for (const restaurant of restaurants.slice(0, 3)) {
        const menuRef = collection(
          firestore,
          "restaurants",
          restaurant.id,
          "menu"
        );
        const menuQuery = query(
          menuRef,
          where("available", "==", true),
          limit(5)
        );
        const snapshot = await getDocs(menuQuery);

        const items = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          restaurantId: restaurant.id,
          restaurantName: restaurant.restaurantName,
        }));

        allItems.push(...items);
      }

      return allItems;
    } catch (error) {
      console.error("Error getting popular menu items:", error);
      return [];
    }
  }

  /**
   * Get restaurants for given menu items
   */
  private async getRestaurantsForMenuItems(menuItems: any[]): Promise<any[]> {
    if (!menuItems.length) return [];

    const restaurantIds = [
      ...new Set(menuItems.map((item) => item.restaurantId).filter(Boolean)),
    ];
    const restaurants: any[] = [];

    for (const restaurantId of restaurantIds) {
      try {
        const restaurantRef = doc(firestore, "restaurants", restaurantId);
        const restaurantSnap = await getDoc(restaurantRef);

        if (restaurantSnap.exists()) {
          restaurants.push({
            id: restaurantSnap.id,
            ...restaurantSnap.data(),
          });
        }
      } catch (error) {
        console.error(`Error fetching restaurant ${restaurantId}:`, error);
      }
    }

    return restaurants;
  }

  /**
   * Get reservation information for restaurants
   */
  private async getReservationInfo(restaurants: any[]): Promise<any[]> {
    // This would typically fetch from a reservations collection
    // For now, return mock data based on restaurant info
    return restaurants.map((restaurant) => ({
      restaurantId: restaurant.id,
      restaurantName: restaurant.restaurantName,
      availableSlots: ["18:00", "19:00", "20:00", "21:00"],
      capacity: restaurant.capacity || 50,
      requirements: restaurant.reservationRequirements || [],
    }));
  }

  /**
   * Get restaurant reviews
   */
  private async getRestaurantReviews(restaurants: any[]): Promise<any[]> {
    // This would typically fetch from a reviews collection
    // For now, return basic rating info from restaurant data
    return restaurants.map((restaurant) => ({
      restaurantId: restaurant.id,
      restaurantName: restaurant.restaurantName,
      rating: restaurant.rating || 4.0,
      reviewCount: restaurant.reviewCount || 0,
      recentReviews: restaurant.recentReviews || [],
    }));
  }

  /**
   * Get user's recent orders
   */
  private async getUserRecentOrders(userId: string): Promise<any[]> {
    try {
      const ordersRef = collection(firestore, "orders");
      const ordersQuery = query(
        ordersRef,
        where("userId", "==", userId),
        orderBy("createdAt", "desc"),
        limit(5)
      );

      const snapshot = await getDocs(ordersQuery);
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error("Error fetching recent orders:", error);
      return [];
    }
  }

  /**
   * Build comprehensive prompt based on query type and data
   */
  private buildComprehensivePrompt(
    userQuery: string,
    queryType: string,
    relevantData: any,
    userContext: any,
    language: string
  ): string {
    const languageInstructions = {
      en: "Respond in English",
      tr: "Türkçe olarak yanıtla",
      az: "Azərbaycan dilində cavab ver",
      ru: "Отвечай на русском языке",
      ar: "أجب باللغة العربية",
    };

    const instruction =
      languageInstructions[language as keyof typeof languageInstructions] ||
      languageInstructions.en;

    let prompt = `Sen profesyonel bir müşteri hizmetleri asistanısın. ${instruction}.

ÖNEMLI TALİMATLAR:
- Sadece verilen gerçek verilerden yararlan, hiçbir şey uydurma
- Kısa, öz ve samimi cevaplar ver
- Her seferinde farklı ifadeler kullan, robotik olma
- Müşteriyi pozitif yönde etkilemeye çalış
- Fiyat, adres, telefon gibi bilgileri tam olarak ver

KULLANICI PROFİLİ:
- Diyet Kısıtlamaları: ${
      userContext?.preferences?.dietaryRestrictions?.join(", ") || "Yok"
    }
- Alerjiler: ${userContext?.preferences?.allergies?.join(", ") || "Yok"}
- Favori Kategoriler: ${
      userContext?.preferences?.favoriteCategories?.join(", ") ||
      "Belirtilmemiş"
    }
- Kalori Hedefi: ${userContext?.goals?.calorieGoal || "Belirtilmemiş"}

`;

    // Add relevant data based on query type
    switch (queryType) {
      case "menu":
        prompt += this.buildMenuDataSection(relevantData);
        break;
      case "restaurant":
        prompt += this.buildRestaurantDataSection(relevantData);
        break;
      case "reservation":
        prompt += this.buildReservationDataSection(relevantData);
        break;
      case "review":
        prompt += this.buildReviewDataSection(relevantData);
        break;
      case "general":
        prompt += this.buildGeneralDataSection(relevantData);
        break;
    }

    prompt += `
KULLANICI SORUSU: "${userQuery}"

Bu soruya yukarıdaki gerçek verilerden yararlanarak cevap ver. Eğer veri yoksa, bunu açıkça belirt ve alternatif öneriler sun.`;

    return prompt;
  }

  private buildMenuDataSection(data: any): string {
    let section = "\nMEVCUT MENÜ ÖĞELERİ:\n";

    if (data.menuItems && data.menuItems.length > 0) {
      data.menuItems.forEach((item: any, index: number) => {
        section += `${index + 1}. **${item.name}** - ${
          item.restaurantName || "Restoran bilgisi yok"
        }
   Fiyat: ${item.price ? `$${item.price}` : "Fiyat bilgisi yok"}
   ${item.description ? `Açıklama: ${item.description}` : ""}
   ${item.calories ? `Kalori: ${item.calories}` : ""}
   ${item.category ? `Kategori: ${item.category}` : ""}
   ${
     item.dietary && item.dietary.length > 0
       ? `Diyet: ${item.dietary.join(", ")}`
       : ""
   }

`;
      });
    } else {
      section += "Bu kriterlere uygun menü öğesi bulunamadı.\n";
    }

    return section;
  }

  private buildRestaurantDataSection(data: any): string {
    let section = "\nMEVCUT RESTORANLAR:\n";

    if (data.restaurants && data.restaurants.length > 0) {
      data.restaurants.forEach((restaurant: any, index: number) => {
        section += `${index + 1}. **${restaurant.restaurantName}**
   ${restaurant.description ? `Açıklama: ${restaurant.description}` : ""}
   ${
     restaurant.cuisines && restaurant.cuisines.length > 0
       ? `Mutfak: ${restaurant.cuisines.join(", ")}`
       : ""
   }
   ${restaurant.priceRange ? `Fiyat Aralığı: ${restaurant.priceRange}` : ""}
   ${restaurant.address ? `Adres: ${restaurant.address}` : ""}
   ${restaurant.phone ? `Telefon: ${restaurant.phone}` : ""}
   ${restaurant.rating ? `Puan: ${restaurant.rating}/5` : ""}

`;
      });

      // Add popular menu items if available
      if (data.menuItems && data.menuItems.length > 0) {
        section += "\nPOPÜLER MENÜ ÖĞELERİ:\n";
        data.menuItems.slice(0, 5).forEach((item: any) => {
          section += `- ${item.name} (${item.restaurantName}) - $${
            item.price || "Fiyat yok"
          }\n`;
        });
      }
    } else {
      section += "Bu kriterlere uygun restoran bulunamadı.\n";
    }

    return section;
  }

  private buildReservationDataSection(data: any): string {
    let section = "\nRESERVASYON BİLGİLERİ:\n";

    if (data.restaurants && data.restaurants.length > 0) {
      data.restaurants.forEach((restaurant: any, index: number) => {
        section += `${index + 1}. **${restaurant.restaurantName}**
   ${restaurant.phone ? `Rezervasyon Tel: ${restaurant.phone}` : ""}
   ${
     restaurant.hours
       ? `Çalışma Saatleri: ${JSON.stringify(restaurant.hours)}`
       : ""
   }
   Kapasite: ${restaurant.capacity || "Belirtilmemiş"}

`;
      });

      if (data.reservations && data.reservations.length > 0) {
        section += "MÜSAIT SAATLER:\n";
        data.reservations.forEach((res: any) => {
          section += `- ${res.restaurantName}: ${
            res.availableSlots?.join(", ") || "Saat bilgisi yok"
          }\n`;
        });
      }
    } else {
      section += "Rezervasyon bilgisi bulunamadı.\n";
    }

    return section;
  }

  private buildReviewDataSection(data: any): string {
    let section = "\nDEĞERLENDİRMELER:\n";

    if (data.restaurants && data.restaurants.length > 0) {
      data.restaurants.forEach((restaurant: any, index: number) => {
        section += `${index + 1}. **${restaurant.restaurantName}**
   Puan: ${restaurant.rating || "Puan yok"}/5
   Değerlendirme Sayısı: ${restaurant.reviewCount || 0}

`;
      });

      if (data.reviews && data.reviews.length > 0) {
        section += "DETAYLI DEĞERLENDİRMELER:\n";
        data.reviews.forEach((review: any) => {
          section += `- ${review.restaurantName}: ${review.rating}/5 (${review.reviewCount} değerlendirme)\n`;
        });
      }
    } else {
      section += "Değerlendirme bilgisi bulunamadı.\n";
    }

    return section;
  }

  private buildGeneralDataSection(data: any): string {
    let section = "\nGENEL BİLGİLER:\n";

    if (data.restaurants && data.restaurants.length > 0) {
      section += "MEVCUT RESTORANLAR:\n";
      data.restaurants.slice(0, 3).forEach((restaurant: any, index: number) => {
        section += `${index + 1}. ${restaurant.restaurantName} - ${
          restaurant.cuisines?.join(", ") || "Mutfak bilgisi yok"
        }\n`;
      });
    }

    if (data.orders && data.orders.length > 0) {
      section += "\nSON SİPARİŞLER:\n";
      data.orders.slice(0, 3).forEach((order: any, index: number) => {
        const date = order.createdAt?.toDate
          ? order.createdAt.toDate().toLocaleDateString()
          : "Tarih yok";
        section += `${index + 1}. ${
          order.restaurantName || "Restoran yok"
        } - ${date}\n`;
      });
    }

    return section;
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(data: any, queryType: string): number {
    let score = 0.5; // Base score

    switch (queryType) {
      case "menu":
        if (data.menuItems && data.menuItems.length > 0) score += 0.3;
        if (data.restaurants && data.restaurants.length > 0) score += 0.2;
        break;
      case "restaurant":
        if (data.restaurants && data.restaurants.length > 0) score += 0.4;
        if (data.menuItems && data.menuItems.length > 0) score += 0.1;
        break;
      case "reservation":
        if (data.restaurants && data.restaurants.length > 0) score += 0.3;
        if (data.reservations && data.reservations.length > 0) score += 0.2;
        break;
      case "review":
        if (data.reviews && data.reviews.length > 0) score += 0.4;
        break;
      case "general":
        if (data.restaurants && data.restaurants.length > 0) score += 0.2;
        if (data.orders && data.orders.length > 0) score += 0.2;
        break;
    }

    return Math.min(1, score);
  }

  /**
   * Extract sources for transparency
   */
  private extractSources(data: any): string[] {
    const sources: string[] = [];

    if (data.restaurants) {
      sources.push(
        ...data.restaurants.map((r: any) => `Restaurant: ${r.restaurantName}`)
      );
    }
    if (data.menuItems) {
      sources.push(...data.menuItems.map((m: any) => `Menu: ${m.name}`));
    }
    if (data.orders) {
      sources.push(`User Orders: ${data.orders.length} recent orders`);
    }

    return sources;
  }
}
