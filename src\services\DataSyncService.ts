import { firestore } from "@/config/firebase";
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  onSnapshot,
  collectionGroup,
} from "firebase/firestore";
import { VectorEmbeddingService } from "./VectorEmbeddingService";

/**
 * Service for synchronizing data and maintaining vector embeddings
 * This service monitors changes in menu items and user profiles
 * and updates their corresponding vector embeddings
 */
export class DataSyncService {
  private vectorService: VectorEmbeddingService;
  private listeners: (() => void)[] = [];

  constructor() {
    this.vectorService = new VectorEmbeddingService();
  }

  /**
   * Initialize data synchronization listeners
   */
  initializeSync(): void {
    // Listen for menu item changes
    this.setupMenuItemSync();
    
    // Listen for user profile changes
    this.setupUserProfileSync();
  }

  /**
   * Setup real-time synchronization for menu items
   */
  private setupMenuItemSync(): void {
    try {
      // Listen to all menu items across all restaurants
      const menuItemsRef = collectionGroup(firestore, "menu");
      const menuQuery = query(menuItemsRef, where("available", "==", true));

      const unsubscribe = onSnapshot(menuQuery, async (snapshot) => {
        console.log("Menu items changed, updating embeddings...");
        
        const changedItems = snapshot.docChanges();
        
        for (const change of changedItems) {
          const itemData = change.doc.data();
          const itemId = change.doc.id;

          try {
            if (change.type === "added" || change.type === "modified") {
              // Create or update embedding for the menu item
              await this.vectorService.createMenuItemEmbedding({
                itemId,
                ...itemData,
              });
              console.log(`Updated embedding for menu item: ${itemData.name}`);
            }
            // Note: For deleted items, embeddings will naturally expire from cache
          } catch (error) {
            console.error(`Error updating embedding for item ${itemId}:`, error);
          }
        }
      });

      this.listeners.push(unsubscribe);
    } catch (error) {
      console.error("Error setting up menu item sync:", error);
    }
  }

  /**
   * Setup real-time synchronization for user profiles
   */
  private setupUserProfileSync(): void {
    try {
      // Listen to client profile changes
      const clientsRef = collection(firestore, "clients");

      const unsubscribe = onSnapshot(clientsRef, async (snapshot) => {
        console.log("User profiles changed, updating embeddings...");
        
        const changedProfiles = snapshot.docChanges();
        
        for (const change of changedProfiles) {
          const profileData = change.doc.data();
          const userId = change.doc.id;

          try {
            if (change.type === "added" || change.type === "modified") {
              // Check if relevant fields changed
              if (this.hasRelevantProfileChanges(profileData)) {
                await this.vectorService.createUserProfileEmbedding(userId, profileData);
                console.log(`Updated embedding for user profile: ${userId}`);
              }
            }
          } catch (error) {
            console.error(`Error updating embedding for user ${userId}:`, error);
          }
        }
      });

      this.listeners.push(unsubscribe);
    } catch (error) {
      console.error("Error setting up user profile sync:", error);
    }
  }

  /**
   * Check if profile changes are relevant for embedding updates
   */
  private hasRelevantProfileChanges(profileData: any): boolean {
    // Check if fields that affect recommendations have changed
    const relevantFields = [
      'mealPreferences',
      'calorieCalculator',
      'dietaryGoals',
      'favoriteRestaurants'
    ];

    // For now, assume all changes are relevant
    // In a production system, you'd compare with previous data
    return true;
  }

  /**
   * Manually sync all menu items (useful for initial setup)
   */
  async syncAllMenuItems(): Promise<void> {
    try {
      console.log("Starting manual sync of all menu items...");
      
      const menuItemsRef = collectionGroup(firestore, "menu");
      const menuQuery = query(menuItemsRef, where("available", "==", true));
      
      const snapshot = await getDocs(menuQuery);
      const totalItems = snapshot.docs.length;
      let processedItems = 0;

      console.log(`Found ${totalItems} menu items to sync`);

      for (const doc of snapshot.docs) {
        try {
          const itemData = doc.data();
          const itemId = doc.id;

          await this.vectorService.createMenuItemEmbedding({
            itemId,
            ...itemData,
          });

          processedItems++;
          
          if (processedItems % 10 === 0) {
            console.log(`Processed ${processedItems}/${totalItems} menu items`);
          }
        } catch (error) {
          console.error(`Error syncing menu item ${doc.id}:`, error);
        }
      }

      console.log(`Completed manual sync of ${processedItems} menu items`);
    } catch (error) {
      console.error("Error in manual menu items sync:", error);
      throw error;
    }
  }

  /**
   * Manually sync all user profiles (useful for initial setup)
   */
  async syncAllUserProfiles(): Promise<void> {
    try {
      console.log("Starting manual sync of all user profiles...");
      
      const clientsRef = collection(firestore, "clients");
      const snapshot = await getDocs(clientsRef);
      const totalProfiles = snapshot.docs.length;
      let processedProfiles = 0;

      console.log(`Found ${totalProfiles} user profiles to sync`);

      for (const doc of snapshot.docs) {
        try {
          const profileData = doc.data();
          const userId = doc.id;

          // Only sync profiles that have meaningful data
          if (this.hasRelevantProfileData(profileData)) {
            await this.vectorService.createUserProfileEmbedding(userId, profileData);
            processedProfiles++;
          }
          
          if (processedProfiles % 5 === 0) {
            console.log(`Processed ${processedProfiles}/${totalProfiles} user profiles`);
          }
        } catch (error) {
          console.error(`Error syncing user profile ${doc.id}:`, error);
        }
      }

      console.log(`Completed manual sync of ${processedProfiles} user profiles`);
    } catch (error) {
      console.error("Error in manual user profiles sync:", error);
      throw error;
    }
  }

  /**
   * Check if user profile has relevant data for embedding
   */
  private hasRelevantProfileData(profileData: any): boolean {
    return !!(
      profileData.mealPreferences ||
      profileData.calorieCalculator ||
      profileData.dietaryGoals ||
      profileData.favoriteRestaurants
    );
  }

  /**
   * Get sync status and statistics
   */
  async getSyncStatus(): Promise<{
    menuItemsCount: number;
    userProfilesCount: number;
    lastSyncTime: Date;
  }> {
    try {
      // Count menu items
      const menuItemsRef = collectionGroup(firestore, "menu");
      const menuQuery = query(menuItemsRef, where("available", "==", true));
      const menuSnapshot = await getDocs(menuQuery);
      const menuItemsCount = menuSnapshot.docs.length;

      // Count user profiles
      const clientsRef = collection(firestore, "clients");
      const clientsSnapshot = await getDocs(clientsRef);
      const userProfilesCount = clientsSnapshot.docs.length;

      return {
        menuItemsCount,
        userProfilesCount,
        lastSyncTime: new Date(),
      };
    } catch (error) {
      console.error("Error getting sync status:", error);
      return {
        menuItemsCount: 0,
        userProfilesCount: 0,
        lastSyncTime: new Date(),
      };
    }
  }

  /**
   * Stop all synchronization listeners
   */
  stopSync(): void {
    this.listeners.forEach(unsubscribe => {
      try {
        unsubscribe();
      } catch (error) {
        console.error("Error unsubscribing from listener:", error);
      }
    });
    this.listeners = [];
    console.log("Data synchronization stopped");
  }

  /**
   * Restart synchronization
   */
  restartSync(): void {
    this.stopSync();
    this.initializeSync();
    console.log("Data synchronization restarted");
  }

  /**
   * Force refresh of all embeddings
   */
  async forceRefreshAllEmbeddings(): Promise<void> {
    try {
      console.log("Starting force refresh of all embeddings...");
      
      // Refresh menu items
      await this.syncAllMenuItems();
      
      // Refresh user profiles
      await this.syncAllUserProfiles();
      
      console.log("Force refresh completed successfully");
    } catch (error) {
      console.error("Error during force refresh:", error);
      throw error;
    }
  }
}
